# ======================================================================
# Campaign Keywords Pipeline - Daily Batch Job Script (PowerShell)
# ======================================================================
# This script runs the campaign keywords processing pipeline
# Designed to be executed by Windows Task Scheduler
# ======================================================================

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# ======================================================================
# CONFIGURATION SECTION - Modify these variables as needed
# ======================================================================

# Python executable path - Update this to match your Python 3.11 installation
# Use 'python' if Python is in PATH, otherwise specify full path
$PythonPath = "python"

# Alternative paths to try if the above doesn't work
$AlternativePythonPaths = @(
    "C:\Python311\python.exe",
    "C:\Program Files\Python311\python.exe",
    "$env:LOCALAPPDATA\Programs\Python\Python311\python.exe",
    "$env:PROGRAMFILES\Python311\python.exe"
)

# Log directory for batch job logs
$BatchLogDir = Join-Path $ScriptDir "batch_logs"

# Create batch log directory if it doesn't exist
if (!(Test-Path $BatchLogDir)) {
    New-Item -ItemType Directory -Path $BatchLogDir | Out-Null
}

# Generate timestamp for log file
$Timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$BatchLog = Join-Path $BatchLogDir "pipeline_batch_$Timestamp.log"

# ======================================================================
# FUNCTIONS
# ======================================================================

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $LogMessage = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [$Level] $Message"
    Add-Content -Path $BatchLog -Value $LogMessage
    
    # Also write to console based on level
    switch ($Level) {
        "ERROR" { Write-Host $LogMessage -ForegroundColor Red }
        "WARNING" { Write-Host $LogMessage -ForegroundColor Yellow }
        default { Write-Host $LogMessage }
    }
}

function Find-Python {
    # First try the configured path
    try {
        $version = & $PythonPath --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Found Python: $version"
            return $PythonPath
        }
    } catch {}
    
    # Try alternative paths
    foreach ($path in $AlternativePythonPaths) {
        if (Test-Path $path) {
            try {
                $version = & $path --version 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Log "Found Python at: $path ($version)"
                    return $path
                }
            } catch {}
        }
    }
    
    # Try to find Python 3.11 in PATH
    try {
        $pythonCmd = Get-Command python -ErrorAction SilentlyContinue
        if ($pythonCmd) {
            $version = & $pythonCmd.Source --version 2>&1
            if ($version -match "3\.11") {
                Write-Log "Found Python 3.11 in PATH: $($pythonCmd.Source)"
                return $pythonCmd.Source
            }
        }
    } catch {}
    
    return $null
}

# ======================================================================
# MAIN EXECUTION
# ======================================================================

try {
    Write-Log "======================================================================"
    Write-Log "Campaign Keywords Pipeline - Batch Job Started"
    Write-Log "Working Directory: $ScriptDir"
    Write-Log "Computer Name: $env:COMPUTERNAME"
    Write-Log "User: $env:USERNAME"
    Write-Log "======================================================================"
    
    # Find Python executable
    Write-Log "Checking Python installation..."
    $Python = Find-Python
    
    if (!$Python) {
        Write-Log "ERROR: Python 3.11 not found. Please install Python 3.11 and update the script." "ERROR"
        exit 1
    }
    
    # Check if main.py exists
    if (!(Test-Path "main.py")) {
        Write-Log "ERROR: main.py not found in $ScriptDir" "ERROR"
        Write-Log "Make sure this script is in the project root directory" "ERROR"
        exit 1
    }
    
    # Check/Install dependencies
    Write-Log "Checking dependencies..."
    $pipOutput = & $Python -m pip install -r requirements.txt --quiet 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log "WARNING: Failed to update dependencies" "WARNING"
        Write-Log "Pip output: $pipOutput" "WARNING"
        Write-Log "Continuing with existing dependencies..."
    } else {
        Write-Log "Dependencies checked/updated successfully"
    }
    
    # Run the pipeline
    Write-Log ""
    Write-Log "Starting pipeline execution..."
    Write-Log "======================================================================"
    
    # Execute the main script and capture output
    $pipelineOutput = & $Python main.py 2>&1 | ForEach-Object {
        $line = $_.ToString()
        Add-Content -Path $BatchLog -Value $line
        Write-Host $line
        $_
    }
    
    $pipelineExitCode = $LASTEXITCODE
    
    Write-Log ""
    Write-Log "======================================================================"
    Write-Log "Pipeline execution completed with exit code: $pipelineExitCode"
    
    if ($pipelineExitCode -ne 0) {
        Write-Log "Pipeline execution failed" "ERROR"
    } else {
        Write-Log "Pipeline execution successful"
    }
    
    # Clean up old batch logs (keep last 30 days)
    Write-Log ""
    Write-Log "Cleaning up old batch logs..."
    
    $oldLogs = Get-ChildItem -Path $BatchLogDir -Filter "pipeline_batch_*.log" | 
               Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-30) }
    
    if ($oldLogs) {
        $oldLogs | Remove-Item -Force
        Write-Log "Removed $($oldLogs.Count) old log files"
    } else {
        Write-Log "No old log files to remove"
    }
    
    # Exit with pipeline exit code
    exit $pipelineExitCode
    
} catch {
    Write-Log "FATAL ERROR: $_" "ERROR"
    Write-Log "Stack Trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
} 
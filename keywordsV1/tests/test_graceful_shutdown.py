"""
Test graceful shutdown handling.
"""
import pytest
import sys
import os
import signal
import threading
import time
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import src.pipeline as pipeline


class TestGracefulShutdown:
    """Test graceful shutdown functionality."""
    
    def test_signal_handler_sets_shutdown_flag(self):
        """Test that signal handler sets the shutdown flag."""
        # Reset shutdown flag
        pipeline.shutdown_requested = False
        
        # Call signal handler
        pipeline.signal_handler(signal.SIGINT, None)
        
        # Should set shutdown flag
        assert pipeline.shutdown_requested is True
    
    def test_signal_handlers_registered(self):
        """Test that signal handlers are properly registered."""
        with patch('signal.signal') as mock_signal:
            # Reload module to trigger signal registration
            import importlib
            importlib.reload(pipeline)
            
            # Should have registered handlers for SIGINT and SIGTERM
            expected_calls = [
                (signal.SIGINT, pipeline.signal_handler),
                (signal.SIGTERM, pipeline.signal_handler)
            ]
            
            for expected_call in expected_calls:
                assert expected_call in mock_signal.call_args_list
    
    def test_pipeline_stops_on_shutdown_request(self):
        """Test that pipeline stops when shutdown is requested."""
        # Reset shutdown flag
        pipeline.shutdown_requested = False
        
        # Mock database and LLM operations
        with patch('src.db.sql_connection') as mock_db, \
             patch('src.db.ensure_tables'), \
             patch('src.db.fetch_patient_ids') as mock_fetch, \
             patch('src.pipeline.process_batch') as mock_process, \
             patch('src.db.update_appointment_keywords'):
            
            # Mock to return patients initially, then trigger shutdown
            mock_fetch.side_effect = [
                ["patient1", "patient2"],  # First call returns patients
                ["patient3", "patient4"]   # Second call (should not be processed)
            ]
            
            mock_db.return_value.__enter__.return_value = MagicMock()
            
            # Mock process_batch to set shutdown flag after first call
            def trigger_shutdown(*args, **kwargs):
                pipeline.shutdown_requested = True
                return {"patient1": {"keywords": [], "reasoning": {}}}
            
            mock_process.side_effect = trigger_shutdown
            
            # Run pipeline
            with patch('src.pipeline.logger') as mock_logger:
                pipeline.main()
            
            # Should have processed only one batch
            assert mock_process.call_count == 1
            
            # Should have logged shutdown message
            shutdown_logs = [call for call in mock_logger.info.call_args_list 
                           if 'shutdown' in str(call).lower()]
            assert len(shutdown_logs) > 0
    
    def test_pipeline_checks_shutdown_before_llm_processing(self):
        """Test that pipeline checks for shutdown before expensive LLM operations."""
        # Reset shutdown flag
        pipeline.shutdown_requested = False
        
        with patch('src.db.sql_connection') as mock_db, \
             patch('src.db.ensure_tables'), \
             patch('src.db.fetch_patient_ids', return_value=["patient1"]), \
             patch('src.db.fetch_batch_patient_data', return_value={"patient1": {"data": "test"}}), \
             patch('utils.hashing.stable_hash', return_value="hash1"), \
             patch('src.db.fetch_existing_hashes', return_value={}), \
             patch('src.pipeline.process_batch') as mock_process:
            
            mock_db.return_value.__enter__.return_value = MagicMock()
            
            # Set shutdown flag before LLM processing
            pipeline.shutdown_requested = True
            
            # Run pipeline
            with patch('src.pipeline.logger') as mock_logger:
                pipeline.main()
            
            # Should not have called process_batch (LLM processing)
            mock_process.assert_not_called()
            
            # Should have logged shutdown before LLM processing
            shutdown_logs = [call for call in mock_logger.info.call_args_list 
                           if 'stopping before llm processing' in str(call).lower()]
            assert len(shutdown_logs) > 0
    
    def test_infinite_loop_prevention(self):
        """Test that infinite loops are prevented with consecutive empty batches."""
        # Reset shutdown flag
        pipeline.shutdown_requested = False
        
        with patch('src.db.sql_connection') as mock_db, \
             patch('src.db.ensure_tables'), \
             patch('src.db.fetch_patient_ids', return_value=[]):  # Always return empty
            
            mock_db.return_value.__enter__.return_value = MagicMock()
            
            # Run pipeline
            with patch('src.pipeline.logger') as mock_logger:
                pipeline.main()
            
            # Should have logged warning about max empty batches
            warning_logs = [call for call in mock_logger.warning.call_args_list 
                          if 'maximum consecutive empty batches' in str(call).lower()]
            assert len(warning_logs) > 0
    
    def test_pipeline_continues_after_llm_errors(self):
        """Test that pipeline continues processing after LLM errors."""
        # Reset shutdown flag
        pipeline.shutdown_requested = False
        
        with patch('src.db.sql_connection') as mock_db, \
             patch('src.db.ensure_tables'), \
             patch('src.db.fetch_patient_ids') as mock_fetch, \
             patch('src.db.fetch_batch_patient_data', return_value={"patient1": {"data": "test"}}), \
             patch('utils.hashing.stable_hash', return_value="hash1"), \
             patch('src.db.fetch_existing_hashes', return_value={}), \
             patch('src.pipeline.process_batch') as mock_process:
            
            # Return patients for first two calls, then empty
            mock_fetch.side_effect = [
                ["patient1"],  # First batch
                ["patient2"],  # Second batch  
                []             # End
            ]
            
            # First call raises ConnectionError, second succeeds
            mock_process.side_effect = [
                ConnectionError("Network error"),
                {"patient2": {"keywords": [], "reasoning": {}}}
            ]
            
            mock_db.return_value.__enter__.return_value = MagicMock()
            
            # Run pipeline
            with patch('src.pipeline.logger') as mock_logger:
                pipeline.main()
            
            # Should have attempted processing twice
            assert mock_process.call_count == 2
            
            # Should have logged the network error
            error_logs = [call for call in mock_logger.error.call_args_list 
                         if 'network' in str(call).lower()]
            assert len(error_logs) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""
Test CSV parsing fixes.
"""
import pytest
import tempfile
import os
import sys
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from src.pipeline import main


class TestCSVParsing:
    """Test the CSV parsing logic fixes."""
    
    def create_test_csv(self, content):
        """Create a temporary CSV file with given content."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        temp_file.write(content)
        temp_file.close()
        return temp_file.name
    
    def test_csv_parsing_multi_column(self):
        """Test that CSV parsing handles multi-column structure correctly."""
        # Create test CSV with multiple columns and duplicates
        csv_content = """Category1,Category2,Category3
diabetes,insulin,glucose
hypertension,blood pressure,medication
diabetes,sugar,insulin
heart disease,cardiac,chest pain
,empty,
diabetes,insulin,glucose"""
        
        csv_file = self.create_test_csv(csv_content)
        
        try:
            # Mock config to use our test CSV
            with patch.object(config, 'CAMPAIGN_KEYWORDS_CSV', csv_file):
                with patch('src.db.sql_connection'), \
                     patch('src.db.ensure_tables'), \
                     patch('src.db.fetch_patient_ids', return_value=[]):
                    
                    # This should not raise an exception and should load keywords
                    main()
                    
        finally:
            os.unlink(csv_file)
    
    def test_csv_parsing_removes_duplicates(self):
        """Test that duplicate keywords are removed."""
        csv_content = """Category1,Category2
diabetes,diabetes
insulin,INSULIN
glucose,glucose"""
        
        csv_file = self.create_test_csv(csv_content)
        
        try:
            # Import the CSV parsing logic directly
            import csv
            campaign_kw = []
            seen_keywords = set()
            
            with open(csv_file, newline="", encoding="utf-8") as f:
                rdr = csv.reader(f)
                next(rdr, None)  # skip header
                
                for row in rdr:
                    for cell in row:
                        keyword = cell.strip().lower()
                        if keyword and keyword not in seen_keywords:
                            campaign_kw.append(keyword)
                            seen_keywords.add(keyword)
            
            # Should have unique keywords only
            assert len(campaign_kw) == 3  # diabetes, insulin, glucose
            assert 'diabetes' in campaign_kw
            assert 'insulin' in campaign_kw
            assert 'glucose' in campaign_kw
            
        finally:
            os.unlink(csv_file)
    
    def test_csv_parsing_empty_file(self):
        """Test handling of empty CSV file."""
        csv_content = """Category1,Category2
,
  ,  """
        
        csv_file = self.create_test_csv(csv_content)
        
        try:
            with patch.object(config, 'CAMPAIGN_KEYWORDS_CSV', csv_file):
                with patch('src.db.sql_connection'), \
                     patch('src.db.ensure_tables'), \
                     patch('src.db.fetch_patient_ids', return_value=[]):
                    
                    # Should raise ValueError for no valid keywords
                    with pytest.raises(ValueError, match="No valid campaign keywords found"):
                        main()
                        
        finally:
            os.unlink(csv_file)
    
    def test_csv_parsing_file_not_found(self):
        """Test handling of missing CSV file."""
        with patch.object(config, 'CAMPAIGN_KEYWORDS_CSV', 'nonexistent.csv'):
            with patch('src.db.sql_connection'), \
                 patch('src.db.ensure_tables'), \
                 patch('src.db.fetch_patient_ids', return_value=[]):
                
                # Should raise FileNotFoundError
                with pytest.raises(FileNotFoundError):
                    main()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

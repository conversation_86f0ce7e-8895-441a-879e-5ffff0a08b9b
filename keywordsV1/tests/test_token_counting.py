"""
Test token counting fallback improvements.
"""
import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hashing import count_tokens, TIKTOKEN_AVAILABLE


class TestTokenCounting:
    """Test the improved token counting functionality."""
    
    def test_token_counting_with_tiktoken(self):
        """Test token counting when tiktoken is available."""
        if TIKTOKEN_AVAILABLE:
            # Test basic functionality
            result = count_tokens("Hello world!")
            assert isinstance(result, int)
            assert result > 0
            
            # Test empty string
            result = count_tokens("")
            assert result == 0
            
            # Test longer text
            long_text = "This is a longer piece of text that should have more tokens."
            result = count_tokens(long_text)
            assert result > 10
        else:
            pytest.skip("tiktoken not available")
    
    def test_token_counting_fallback(self):
        """Test token counting fallback when tiktoken is not available."""
        # Mock tiktoken as unavailable
        with patch.dict('sys.modules', {'tiktoken': None}):
            # Reload the module to trigger ImportError
            import importlib
            import utils.hashing
            importlib.reload(utils.hashing)
            
            # Test fallback functionality
            result = utils.hashing.count_tokens("Hello world!")
            assert isinstance(result, int)
            assert result > 0
            
            # Test empty string
            result = utils.hashing.count_tokens("")
            assert result == 0
            
            # Test that fallback is more conservative
            long_text = "This is a longer piece of text that should have more tokens than simple word count."
            result = utils.hashing.count_tokens(long_text)
            word_count = len(long_text.split())
            assert result >= word_count  # Should be conservative
    
    def test_token_counting_edge_cases(self):
        """Test token counting with edge cases."""
        # Test non-string input
        result = count_tokens(123)
        assert isinstance(result, int)
        
        # Test None input (converted to string)
        result = count_tokens(None)
        assert isinstance(result, int)
        
        # Test whitespace-only string
        result = count_tokens("   \n\t   ")
        assert result >= 0
        
        # Test special characters
        result = count_tokens("!@#$%^&*()")
        assert result > 0
    
    def test_token_counting_accuracy_comparison(self):
        """Compare fallback accuracy with expected ranges."""
        test_texts = [
            "Hello",
            "Hello world",
            "This is a test sentence.",
            "This is a much longer sentence with many more words and punctuation marks!",
            "JSON data: {'key': 'value', 'number': 42, 'list': [1, 2, 3]}"
        ]
        
        for text in test_texts:
            result = count_tokens(text)
            word_count = len(text.split())
            char_count = len(text)
            
            # Fallback should be between word count and char count / 2
            assert result >= word_count
            assert result <= char_count  # Should be reasonable upper bound
    
    def test_token_counting_warning_logged_once(self):
        """Test that fallback warning is logged only once."""
        if not TIKTOKEN_AVAILABLE:
            # Reset the warning flag if it exists
            if hasattr(count_tokens, '_warned'):
                delattr(count_tokens, '_warned')
            
            with patch('logging.getLogger') as mock_logger:
                mock_log = MagicMock()
                mock_logger.return_value = mock_log
                
                # Call multiple times
                count_tokens("test 1")
                count_tokens("test 2")
                count_tokens("test 3")
                
                # Warning should be logged only once
                warning_calls = [call for call in mock_log.warning.call_args_list 
                               if 'fallback token counting' in str(call)]
                assert len(warning_calls) <= 1
        else:
            pytest.skip("tiktoken is available, fallback not used")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

@echo off
REM ======================================================================
REM Campaign Keywords Pipeline - Daily Batch Job Script
REM ======================================================================
REM This script runs the campaign keywords processing pipeline
REM Designed to be executed by Windows Task Scheduler
REM ======================================================================

REM Set the working directory to the script's location
cd /d "%~dp0"

REM ======================================================================
REM CONFIGURATION SECTION - Modify these variables as needed
REM ======================================================================

REM Python executable path - Update this to match your Python 3.11 installation
REM Common locations:
REM - C:\Python311\python.exe
REM - C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
REM - C:\Program Files\Python311\python.exe
set PYTHON_PATH=python

REM Log directory for batch job logs (not the application logs)
set BATCH_LOG_DIR=%cd%\batch_logs

REM Create batch log directory if it doesn't exist
if not exist "%BATCH_LOG_DIR%" mkdir "%BATCH_LOG_DIR%"

REM Generate timestamp for log file
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c-%%a-%%b)
for /f "tokens=1-2 delims=/:" %%a in ('time /t') do (set mytime=%%a%%b)
set mytime=%mytime: =_%
set TIMESTAMP=%mydate%_%mytime%

REM Batch job log file
set BATCH_LOG=%BATCH_LOG_DIR%\pipeline_batch_%TIMESTAMP%.log

REM ======================================================================
REM MAIN EXECUTION
REM ======================================================================

echo ====================================================================== >> "%BATCH_LOG%" 2>&1
echo Campaign Keywords Pipeline - Batch Job Started >> "%BATCH_LOG%" 2>&1
echo Date: %date% >> "%BATCH_LOG%" 2>&1
echo Time: %time% >> "%BATCH_LOG%" 2>&1
echo Working Directory: %cd% >> "%BATCH_LOG%" 2>&1
echo ====================================================================== >> "%BATCH_LOG%" 2>&1
echo. >> "%BATCH_LOG%" 2>&1

REM Check if Python is available
echo Checking Python installation... >> "%BATCH_LOG%" 2>&1
%PYTHON_PATH% --version >> "%BATCH_LOG%" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python not found at %PYTHON_PATH% >> "%BATCH_LOG%" 2>&1
    echo Please update PYTHON_PATH in this script >> "%BATCH_LOG%" 2>&1
    exit /b 1
)

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found in %cd% >> "%BATCH_LOG%" 2>&1
    echo Make sure this script is in the project root directory >> "%BATCH_LOG%" 2>&1
    exit /b 1
)

REM Install/Update dependencies if requirements.txt has changed
echo Checking dependencies... >> "%BATCH_LOG%" 2>&1
%PYTHON_PATH% -m pip install -r requirements.txt --quiet >> "%BATCH_LOG%" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to update dependencies >> "%BATCH_LOG%" 2>&1
    echo Continuing with existing dependencies... >> "%BATCH_LOG%" 2>&1
)

REM Run the pipeline
echo. >> "%BATCH_LOG%" 2>&1
echo Starting pipeline execution... >> "%BATCH_LOG%" 2>&1
echo ====================================================================== >> "%BATCH_LOG%" 2>&1
echo. >> "%BATCH_LOG%" 2>&1

REM Execute the main script and capture both stdout and stderr
%PYTHON_PATH% main.py >> "%BATCH_LOG%" 2>&1
set PIPELINE_EXIT_CODE=%ERRORLEVEL%

echo. >> "%BATCH_LOG%" 2>&1
echo ====================================================================== >> "%BATCH_LOG%" 2>&1
echo Pipeline execution completed with exit code: %PIPELINE_EXIT_CODE% >> "%BATCH_LOG%" 2>&1
echo End Time: %time% >> "%BATCH_LOG%" 2>&1
echo ====================================================================== >> "%BATCH_LOG%" 2>&1

REM Clean up old batch logs (keep last 30 days)
echo. >> "%BATCH_LOG%" 2>&1
echo Cleaning up old batch logs... >> "%BATCH_LOG%" 2>&1
forfiles /p "%BATCH_LOG_DIR%" /s /m pipeline_batch_*.log /d -30 /c "cmd /c del @path" 2>nul

REM Exit with the same code as the pipeline
exit /b %PIPELINE_EXIT_CODE% 
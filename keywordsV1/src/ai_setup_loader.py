"""
AI Setup configuration loader module for reading settings from the EducationalContent_AISetup table.

This module provides functionality to load AI-specific configuration values from
the EducationalContent_AISetup table in the CAEnterprise database.

The AI setup database contains configuration for:
- LLM settings (API keys, model names, temperature, etc.)
- Processing parameters (batch size, keywords per patient, etc.)
- Cost and retry limits
- File paths and reasoning settings
"""

import logging
from typing import Any, Dict, Optional
import pyodbc

logger = logging.getLogger("ai_setup_loader")


def load_ai_setup_from_db(conn_str: str, fallback_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Load AI setup configuration values from the EducationalContent_AISetup table.

    Args:
        conn_str: Database connection string for CAEnterprise database
        fallback_config: Optional dictionary of fallback values if database read fails

    Returns:
        Dictionary of configuration key-value pairs mapped from database columns

    Raises:
        pyodbc.Error: If database connection or query fails
    """
    config_dict = {}
    fallback_config = fallback_config or {}

    try:
        # Connect to database
        with pyodbc.connect(conn_str, timeout=30) as conn:
            with conn.cursor() as cursor:
                # Query active AI setup configuration
                cursor.execute("""
                    SELECT TOP 1
                        AILLM, AIModel, AIKey, NoOfPatientPerBatch, IsAIReasoning,
                        NoofKeywordsToFetch, MaxCostPerBatch, LLMTemperature, 
                        LLMRtetryAttempts, KeywordFilePath, Source
                    FROM dbo.EducationalContent_AISetup
                    WHERE IsActive = 'Y'
                    ORDER BY EducationalContentAISetupId DESC
                """)
                
                row = cursor.fetchone()
                
                if not row:
                    logger.warning("No active AI setup configuration found in database")
                    logger.warning("Database: CAEnterprise, Table: EducationalContent_AISetup")
                    logger.info("Using manually set default values from code")
                    return fallback_config
                
                # Map database columns to config keys
                config_dict = {
                    'GEMINI_API_KEY': row[2] if row[2] else fallback_config.get('GEMINI_API_KEY'),
                    'BATCH_SIZE': row[3] if row[3] is not None else fallback_config.get('BATCH_SIZE', 10),
                    'REASONING_REQUIRED': row[4] == 'Y' if row[4] else fallback_config.get('REASONING_REQUIRED', True),
                    'KEYWORDS_PER_PATIENT': row[5] if row[5] is not None else fallback_config.get('KEYWORDS_PER_PATIENT', 7),
                    'MAX_COST_USD': float(row[6]) if row[6] is not None else fallback_config.get('MAX_COST_USD', 5.0),
                    'LLM_TEMPERATURE': float(row[7]) if row[7] is not None else fallback_config.get('LLM_TEMPERATURE', 0.2),
                    'LLM_RETRY_ATTEMPTS': row[8] if row[8] is not None else fallback_config.get('LLM_RETRY_ATTEMPTS', 4),
                    'CAMPAIGN_KEYWORDS_CSV': row[9] if row[9] else fallback_config.get('CAMPAIGN_KEYWORDS_CSV', 'data/CampaignKeywords.csv'),
                    
                    # Additional info for logging/debugging
                    'AI_LLM_PROVIDER': row[0] if row[0] else 'Gemini',
                    'AI_MODEL_NAME': row[1] if row[1] else 'Gemini 1.5 Flash',
                    'AI_CONFIG_SOURCE': row[10] if row[10] else 'EducationalContent_AISetup',
                    
                    # Track which values came from database vs fallback
                    '_DB_SOURCES': {
                        'GEMINI_API_KEY': bool(row[2]),
                        'BATCH_SIZE': row[3] is not None,
                        'REASONING_REQUIRED': bool(row[4]),
                        'KEYWORDS_PER_PATIENT': row[5] is not None,
                        'MAX_COST_USD': row[6] is not None,
                        'LLM_TEMPERATURE': row[7] is not None,
                        'LLM_RETRY_ATTEMPTS': row[8] is not None,
                        'CAMPAIGN_KEYWORDS_CSV': bool(row[9])
                    }
                }
                
                logger.info(f"Successfully loaded AI setup configuration from database")
                logger.info(f"Database: CAEnterprise, Table: EducationalContent_AISetup")
                logger.info(f"LLM Provider: {config_dict['AI_LLM_PROVIDER']}, Model: {config_dict['AI_MODEL_NAME']}")
                logger.info(f"Configuration loaded from database table:")
                logger.info(f"  - GEMINI_API_KEY: {'✓ From DB' if row[2] else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - BATCH_SIZE: {config_dict['BATCH_SIZE']} {'✓ From DB' if row[3] is not None else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - REASONING_REQUIRED: {config_dict['REASONING_REQUIRED']} {'✓ From DB' if row[4] else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - KEYWORDS_PER_PATIENT: {config_dict['KEYWORDS_PER_PATIENT']} {'✓ From DB' if row[5] is not None else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - MAX_COST_USD: {config_dict['MAX_COST_USD']} {'✓ From DB' if row[6] is not None else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - LLM_TEMPERATURE: {config_dict['LLM_TEMPERATURE']} {'✓ From DB' if row[7] is not None else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - LLM_RETRY_ATTEMPTS: {config_dict['LLM_RETRY_ATTEMPTS']} {'✓ From DB' if row[8] is not None else '✗ From DEFAULT_CONFIG'}")
                logger.info(f"  - CAMPAIGN_KEYWORDS_CSV: {config_dict['CAMPAIGN_KEYWORDS_CSV']} {'✓ From DB' if row[9] else '✗ From DEFAULT_CONFIG'}")
                
        # Add any missing values from fallback config
        for key, value in fallback_config.items():
            if key not in config_dict:
                config_dict[key] = value
                logger.debug(f"Added fallback config: {key} = {value}")
        
        return config_dict
        
    except pyodbc.Error as e:
        logger.error(f"Database error while loading AI setup configuration: {e}")
        if fallback_config:
            logger.warning("Using fallback configuration values")
            return fallback_config
        raise
    except Exception as e:
        logger.error(f"Unexpected error while loading AI setup configuration: {e}")
        if fallback_config:
            logger.warning("Using fallback configuration values")
            return fallback_config
        raise


def get_ai_setup_connection_string(
    driver: str = "ODBC Driver 18 for SQL Server",
    server: str = "************",
    database: str = "CAEnterprise",
    username: str = "devAdmin",
    password: str = "hOstIR&8l8lWrl=7SlDr",
    encrypt: str = "no",
    trust_cert: str = "yes"
) -> str:
    """
    Build connection string for AI setup configuration database (CAEnterprise).
    
    Args:
        driver: ODBC driver name
        server: Database server address
        database: Database name (CAEnterprise)
        username: Database username
        password: Database password
        encrypt: Encryption setting
        trust_cert: Trust server certificate setting
        
    Returns:
        Connection string for pyodbc
    """
    return (
        f"DRIVER={{{driver}}};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={username};"
        f"PWD={password};"
        f"Encrypt={encrypt};"
        f"TrustServerCertificate={trust_cert};"
    ) 
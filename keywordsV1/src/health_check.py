"""
Health check module for monitoring system health and readiness.

This module provides health check functionality to monitor:
- Database connectivity
- LLM API availability
- Disk space
- Memory usage
- Configuration loading

The health checks can be used by orchestrators (Kubernetes, Docker)
or monitoring systems to ensure the application is running correctly.
"""

import os
import psutil
import logging
from datetime import datetime
from typing import Dict, Any, Tuple
import pyodbc
import google.generativeai as genai
from contextlib import contextmanager
import threading
import time
from flask import Flask, jsonify
import config

logger = logging.getLogger("health_check")

app = Flask(__name__)

class HealthStatus:
    """Health status constants"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"

class HealthChecker:
    """
    Main health checker class that performs various system health checks.
    
    This class provides methods to check:
    - Database connectivity (both configuration and keyword databases)
    - LLM API availability
    - System resources (disk space, memory)
    - Application readiness
    """
    
    def __init__(self):
        self.last_check_time = {}
        self.check_results = {}
        self.cache_duration = 30  # Cache results for 30 seconds
        
    def _is_cache_valid(self, check_name: str) -> bool:
        """Check if cached result is still valid"""
        if check_name not in self.last_check_time:
            return False
        
        elapsed = (datetime.now() - self.last_check_time[check_name]).total_seconds()
        return elapsed < self.cache_duration
    
    def _cache_result(self, check_name: str, result: Dict[str, Any]):
        """Cache health check result"""
        self.check_results[check_name] = result
        self.last_check_time[check_name] = datetime.now()
    
    def check_database_connectivity(self, connection_type: str = "keyword") -> Dict[str, Any]:
        """
        Check database connectivity for either configuration or keyword database.
        
        Args:
            connection_type: Either "config" or "keyword" database
            
        Returns:
            Dict with status and optional error message
        """
        check_name = f"database_{connection_type}"
        
        # Return cached result if valid
        if self._is_cache_valid(check_name):
            return self.check_results[check_name]
        
        try:
            # Build connection string based on type
            if connection_type == "config":
                conn_str = (
                    f"DRIVER={{{config.CONFIG_DB_DRIVER}}};"
                    f"SERVER={config.CONFIG_DB_SERVER};"
                    f"DATABASE={config.CONFIG_DB_NAME};"
                    f"UID={config.CONFIG_DB_USERNAME};"
                    f"PWD={config.CONFIG_DB_PASSWORD};"
                    f"Encrypt={config.CONFIG_DB_ENCRYPT};"
                    f"TrustServerCertificate={config.CONFIG_DB_TRUST_CERT};"
                )
            else:  # keyword database
                conn_str = (
                    f"DRIVER={{{config.DB_DRIVER}}};"
                    f"SERVER={config.DB_SERVER};"
                    f"DATABASE={config.DB_NAME};"
                    f"UID={config.DB_USERNAME};"
                    f"PWD={config.DB_PASSWORD};"
                    f"Encrypt={config.DB_ENCRYPT};"
                    f"TrustServerCertificate={config.DB_TRUST_CERT};"
                )
            
            # Test connection with short timeout
            with pyodbc.connect(conn_str, timeout=5) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            
            result = {
                "status": HealthStatus.HEALTHY,
                "message": f"{connection_type} database is accessible",
                "timestamp": datetime.now().isoformat()
            }
            
        except pyodbc.Error as e:
            logger.error(f"Database health check failed for {connection_type}: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Unexpected error in database health check: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": f"Unexpected error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        
        self._cache_result(check_name, result)
        return result
    
    def check_llm_api(self) -> Dict[str, Any]:
        """
        Check Google Gemini API availability.
        
        Returns:
            Dict with status and optional error message
        """
        check_name = "llm_api"
        
        # Return cached result if valid
        if self._is_cache_valid(check_name):
            return self.check_results[check_name]
        
        try:
            if not config.GEMINI_API_KEY:
                raise ValueError("GEMINI_API_KEY not configured")
            
            # Configure API
            genai.configure(api_key=config.GEMINI_API_KEY)
            
            # List available models as a health check
            models = genai.list_models()
            model_count = len(list(models))
            
            result = {
                "status": HealthStatus.HEALTHY,
                "message": f"Gemini API is accessible, {model_count} models available",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"LLM API health check failed: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        self._cache_result(check_name, result)
        return result
    
    def check_disk_space(self, min_gb: float = 1.0) -> Dict[str, Any]:
        """
        Check available disk space.
        
        Args:
            min_gb: Minimum required GB of free space
            
        Returns:
            Dict with status and disk usage information
        """
        check_name = "disk_space"
        
        try:
            # Get disk usage for current directory
            usage = psutil.disk_usage('/')
            free_gb = usage.free / (1024 ** 3)
            used_percent = usage.percent
            
            if free_gb < min_gb:
                status = HealthStatus.UNHEALTHY
                message = f"Low disk space: {free_gb:.2f}GB free (minimum: {min_gb}GB)"
            elif used_percent > 90:
                status = HealthStatus.DEGRADED
                message = f"High disk usage: {used_percent:.1f}% used"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk space OK: {free_gb:.2f}GB free ({100-used_percent:.1f}% available)"
            
            result = {
                "status": status,
                "message": message,
                "metrics": {
                    "free_gb": round(free_gb, 2),
                    "used_percent": round(used_percent, 1),
                    "total_gb": round(usage.total / (1024 ** 3), 2)
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Disk space health check failed: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        self._cache_result(check_name, result)
        return result
    
    def check_memory_usage(self, max_percent: float = 90.0) -> Dict[str, Any]:
        """
        Check system memory usage.
        
        Args:
            max_percent: Maximum acceptable memory usage percentage
            
        Returns:
            Dict with status and memory usage information
        """
        check_name = "memory_usage"
        
        try:
            memory = psutil.virtual_memory()
            
            if memory.percent > max_percent:
                status = HealthStatus.UNHEALTHY
                message = f"High memory usage: {memory.percent:.1f}% (max: {max_percent}%)"
            elif memory.percent > 80:
                status = HealthStatus.DEGRADED
                message = f"Elevated memory usage: {memory.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage OK: {memory.percent:.1f}%"
            
            result = {
                "status": status,
                "message": message,
                "metrics": {
                    "used_percent": round(memory.percent, 1),
                    "available_gb": round(memory.available / (1024 ** 3), 2),
                    "total_gb": round(memory.total / (1024 ** 3), 2)
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Memory usage health check failed: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        self._cache_result(check_name, result)
        return result
    
    def check_configuration(self) -> Dict[str, Any]:
        """
        Check if all required configuration values are present.
        
        Returns:
            Dict with status and configuration check results
        """
        check_name = "configuration"
        
        try:
            missing = []
            
            # Check required configuration values
            required_configs = [
                ("GEMINI_API_KEY", config.GEMINI_API_KEY),
                ("DB_SERVER", config.DB_SERVER),
                ("DB_USERNAME", config.DB_USERNAME),
                ("DB_PASSWORD", config.DB_PASSWORD),
                ("CAMPAIGN_KEYWORDS_CSV", config.CAMPAIGN_KEYWORDS_CSV)
            ]
            
            for config_name, config_value in required_configs:
                if not config_value:
                    missing.append(config_name)
            
            # Check if campaign keywords file exists
            if config.CAMPAIGN_KEYWORDS_CSV and not os.path.exists(config.CAMPAIGN_KEYWORDS_CSV):
                missing.append(f"Campaign keywords file not found: {config.CAMPAIGN_KEYWORDS_CSV}")
            
            if missing:
                status = HealthStatus.UNHEALTHY
                message = f"Missing required configuration: {', '.join(missing)}"
            else:
                status = HealthStatus.HEALTHY
                message = "All required configuration values present"
            
            result = {
                "status": status,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            if missing:
                result["missing_configs"] = missing
            
        except Exception as e:
            logger.error(f"Configuration health check failed: {e}")
            result = {
                "status": HealthStatus.UNHEALTHY,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        self._cache_result(check_name, result)
        return result
    
    def get_all_health_checks(self) -> Tuple[Dict[str, Any], int]:
        """
        Run all health checks and return aggregated results.
        
        Returns:
            Tuple of (results dict, HTTP status code)
        """
        checks = {
            "configuration": self.check_configuration(),
            "config_database": self.check_database_connectivity("config"),
            "keyword_database": self.check_database_connectivity("keyword"),
            "llm_api": self.check_llm_api(),
            "disk_space": self.check_disk_space(),
            "memory_usage": self.check_memory_usage()
        }
        
        # Determine overall health status
        all_healthy = all(
            check.get("status") == HealthStatus.HEALTHY 
            for check in checks.values()
        )
        
        any_unhealthy = any(
            check.get("status") == HealthStatus.UNHEALTHY 
            for check in checks.values()
        )
        
        if all_healthy:
            overall_status = HealthStatus.HEALTHY
            status_code = 200
        elif any_unhealthy:
            overall_status = HealthStatus.UNHEALTHY
            status_code = 503
        else:
            overall_status = HealthStatus.DEGRADED
            status_code = 200
        
        result = {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "checks": checks
        }
        
        return result, status_code

# Create global health checker instance
health_checker = HealthChecker()

@app.route('/health')
def health():
    """Main health check endpoint"""
    result, status_code = health_checker.get_all_health_checks()
    return jsonify(result), status_code

@app.route('/health/live')
def liveness():
    """Kubernetes liveness probe - checks if app is running"""
    return jsonify({
        "status": HealthStatus.HEALTHY,
        "message": "Application is running",
        "timestamp": datetime.now().isoformat()
    }), 200

@app.route('/health/ready')
def readiness():
    """Kubernetes readiness probe - checks if app is ready to serve traffic"""
    # Check only critical components for readiness
    config_check = health_checker.check_configuration()
    keyword_db_check = health_checker.check_database_connectivity("keyword")
    
    if (config_check.get("status") == HealthStatus.HEALTHY and
        keyword_db_check.get("status") == HealthStatus.HEALTHY):
        return jsonify({
            "status": HealthStatus.HEALTHY,
            "message": "Application is ready",
            "timestamp": datetime.now().isoformat()
        }), 200
    else:
        return jsonify({
            "status": HealthStatus.UNHEALTHY,
            "message": "Application is not ready",
            "timestamp": datetime.now().isoformat()
        }), 503

def start_health_api(port: int = 8080):
    """
    Start the health check API in a separate thread.
    
    Args:
        port: Port to run the health API on
    """
    def run_app():
        logger.info(f"Starting health check API on port {port}")
        app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
    
    thread = threading.Thread(target=run_app, daemon=True)
    thread.start()
    
    # Give the server a moment to start
    time.sleep(1)
    logger.info(f"Health check API started on http://0.0.0.0:{port}/health")

# Example usage in main application
if __name__ == "__main__":
    # For testing the health checks
    logging.basicConfig(level=logging.INFO)
    start_health_api()
    
    # Keep the main thread alive
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down health check API") 
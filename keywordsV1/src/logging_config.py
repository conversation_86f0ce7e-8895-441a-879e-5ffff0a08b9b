"""
Logging configuration with rotation support.

This module provides centralized logging configuration with:
- Rotating file handlers to prevent disk space issues
- Structured logging format
- Multiple log levels
- Single log file for all components
"""

import os
import logging
import logging.handlers
from datetime import datetime
import config

def setup_logging(
    log_file: str = None,
    log_level: str = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10 MB
    backup_count: int = 5,
    enable_console: bool = True
) -> logging.Logger:
    """
    Configure logging with rotation support.
    
    Args:
        log_file: Path to log file (uses config default if not provided)
        log_level: Logging level (uses config default if not provided)
        max_bytes: Maximum size of log file before rotation (default: 10MB)
        backup_count: Number of backup files to keep (default: 5)
        enable_console: Whether to also log to console
        
    Returns:
        Configured logger instance
    """
    # Use defaults from config if not provided
    log_file = log_file or config.LOG_FILE
    log_level = log_level or config.LOG_LEVEL
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Convert relative path to absolute path
    log_file = os.path.abspath(log_file)
    
    # Create formatter with structured format
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create rotating file handler with explicit mode='a' (append)
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            mode='a',
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8',
            delay=False  # Create file immediately
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, log_level.upper()))
    except Exception as e:
        print(f"ERROR: Failed to create file handler for {log_file}: {e}")
        raise
    
    # Create console handler if enabled
    handlers = [file_handler]
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        handlers.append(console_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        force=True  # Force reconfiguration
    )
    
    # Return logger
    logger = logging.getLogger()
    logger.info(f"Logging initialized - Level: {log_level}, File: {log_file}, Max Size: {max_bytes/1024/1024:.1f}MB, Backups: {backup_count}")
    
    # Force flush to ensure initial message is written
    for handler in logger.handlers:
        if hasattr(handler, 'flush'):
            handler.flush()
    
    return logger

def get_component_logger(
    component_name: str,
    log_file_suffix: str = None,
    separate_file: bool = False
) -> logging.Logger:
    """
    Get a logger for a specific component.
    
    Since we're using a single log file, this now just returns a named logger
    that writes to the same file as all other loggers.
    
    Args:
        component_name: Name of the component
        log_file_suffix: Ignored (kept for compatibility)
        separate_file: Ignored (kept for compatibility)
        
    Returns:
        Component-specific logger
    """
    # Just return a named logger - it will use the root logger's handlers
    logger = logging.getLogger(component_name)
    return logger

class StructuredLogger:
    """
    Wrapper for structured logging with additional context.
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.context = {}
    
    def set_context(self, **kwargs):
        """Set persistent context for all log messages"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """Clear all context"""
        self.context = {}
    
    def _format_message(self, msg: str, **kwargs) -> str:
        """Format message with context"""
        # Merge context with provided kwargs
        all_context = {**self.context, **kwargs}
        
        if all_context:
            # Format context as key=value pairs
            context_str = " | ".join(f"{k}={v}" for k, v in all_context.items())
            return f"{msg} | {context_str}"
        
        return msg
    
    def debug(self, msg: str, **kwargs):
        """Log debug message with context"""
        self.logger.debug(self._format_message(msg, **kwargs))
    
    def info(self, msg: str, **kwargs):
        """Log info message with context"""
        self.logger.info(self._format_message(msg, **kwargs))
    
    def warning(self, msg: str, **kwargs):
        """Log warning message with context"""
        self.logger.warning(self._format_message(msg, **kwargs))
    
    def error(self, msg: str, **kwargs):
        """Log error message with context"""
        self.logger.error(self._format_message(msg, **kwargs))
    
    def exception(self, msg: str, **kwargs):
        """Log exception with context"""
        self.logger.exception(self._format_message(msg, **kwargs))

def setup_pipeline_logging():
    """
    Setup logging specifically for the pipeline application.
    
    Creates a single log file with rotation for all components.
    """
    # Setup main logger with rotation
    main_logger = setup_logging(
        log_file=config.LOG_FILE,
        log_level=config.LOG_LEVEL,
        max_bytes=50 * 1024 * 1024,  # 50 MB for main log
        backup_count=10,
        enable_console=True
    )
    
    # Create component loggers that all write to the same file
    health_logger = get_component_logger("health_check")
    db_logger = get_component_logger("database")
    llm_logger = get_component_logger("llm")
    pipeline_logger = get_component_logger("patient_pipeline")
    
    # Log startup information
    main_logger.info("=" * 80)
    main_logger.info("Pipeline logging system initialized")
    main_logger.info(f"Single log file: {config.LOG_FILE}")
    main_logger.info(f"Log level: {config.LOG_LEVEL}")
    main_logger.info(f"Rotation: 50MB per file, keeping 10 backups")
    main_logger.info("=" * 80)
    
    return {
        'main': main_logger,
        'health': health_logger,
        'database': db_logger,
        'llm': llm_logger,
        'patient_pipeline': pipeline_logger
    }

# Example usage
if __name__ == "__main__":
    # Test the logging setup
    loggers = setup_pipeline_logging()
    
    # Test main logger
    loggers['main'].info("This is a test message from main logger")
    
    # Test structured logging
    struct_logger = StructuredLogger(loggers['main'])
    struct_logger.set_context(patient_id=12345, batch_id="batch_001")
    struct_logger.info("Processing patient", status="started")
    struct_logger.info("Patient processed", status="completed", duration_ms=150)
    
    # Test component loggers - all will write to the same file
    loggers['health'].info("Health check completed", status="healthy")
    loggers['database'].info("Database connection established", server=config.DB_SERVER)
    loggers['llm'].info("LLM request sent", tokens=1000, cost_usd=0.025)
    loggers['patient_pipeline'].info("Pipeline processing batch", batch_size=10) 
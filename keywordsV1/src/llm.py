"""
Large Language Model (LLM) integration module for keyword generation.

This module handles all interactions with Google's Gemini LLM service for
generating campaign keywords from patient clinical data. It includes:
- Prompt engineering with detailed instructions and validation
- Token counting and cost estimation
- Retry logic for robust API calls
- Gemini API integration

The module uses sophisticated prompts to ensure the LLM generates the configured number of keywords
per patient from a predefined campaign list, with specific clinical reasoning.
"""

import logging
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential_jitter,
)
import config
from utils.hashing import count_tokens
import json
import time

# Use the dedicated LLM logger for better log separation
logger = logging.getLogger("llm")

PROMPT_HEADER_WITH_REASONING = """
    ████████████████████████████████████████████████████████████████████████████████████
    **STOP: ONLY USE THE FOLLOWING CAMPAIGN KEYWORDS. DO NOT USE ANY OTHER TERMS.**
    ████████████████████████████████████████████████████████████████████████████████████

    **CAMPAIGN KEYWORDS (the ONLY valid options, numbered for reference):**
    {campaign_list}

    (You may also refer to the keywords as a comma-separated list: {campaign_csv})

    ████████████████████████████████████████████████████████████████████████████████████
    **REASONING QUALITY WARNING**
    **NEVER use vague terms like "common symptom", "broadly relevant", "possible condition", "general health", etc.**
    **ALWAYS cite specific patient data (medication, diagnosis, symptom, or procedure) in every reasoning.**
    ████████████████████████████████████████████████████████████████████████████████████

    ████████████████████████████████████████████████████████████████████████████████████
    **REASONING QUALITY WARNING**
    **NEVER use vague terms like "common symptom", "broadly relevant", "possible condition", "general health", etc.**
    **ALWAYS cite specific patient data (medication, diagnosis, symptom, or procedure) in every reasoning.**
    ████████████████████████████████████████████████████████████████████████████████████

    **FORBIDDEN Reasoning Examples (NEVER use):**
    ❌ "Common symptom"
    ❌ "Broadly relevant symptom"
    ❌ "Possible underlying condition"
    ❌ "General health concern"
    ❌ "Selected to cover broad range"

    **REQUIRED Reasoning Examples (ALWAYS use):**
    ✅ "Patient diagnosed with [condition] documented in medical history, relates to [campaign_keyword]"
    ✅ "Patient prescribed [medication_name] for [condition], relates to [campaign_keyword]"
    ✅ "Patient has documented [symptom/finding], relates to [campaign_keyword]"
    ✅ "Patient's documented [condition] clinically relates to [campaign_keyword]"

    **MANDATORY CHECKLIST FOR EVERY KEYWORD AND REASONING:**
    1. Is this keyword in the numbered campaign keywords list above? If NO, do NOT use it.
    2. Does the reasoning cite a specific patient data element (medication, diagnosis, symptom, or procedure)?
    3. Does the reasoning avoid all vague terms listed above?
    4. If you cannot find {keywords_per_patient} direct matches, select the most clinically relevant from the campaign list, but do NOT invent or use any other terms.
    5. After generating your answer, re-check that every keyword is in the campaign keywords list above AND that no reasoning contains any forbidden vague terms. If any do, replace them with specific, data-driven reasoning.

    **EXAMPLE OF INVALID OUTPUT (do NOT do this):**
    "keywords": ["muscle spasms", "insomnia", "hypertension", "heartburn", "asthma", "high cholesterol", "memory loss"]
    (❌ 'muscle spasms', 'insomnia', 'heartburn', 'high cholesterol', 'memory loss' are NOT in the campaign keywords list)

    **EXAMPLE OF INVALID REASONING (do NOT do this):**
    "nausea": "Common symptom"

    **CORRECTED OUTPUT:**
    "keywords": [<{keywords_per_patient} keywords ONLY from the campaign keywords list above>]
    "nausea": "Patient has documented complaint of nausea in the medical record, relates to campaign keyword 'nausea'"

    **AI Agent Task: Campaign Keyword Selection for {patient_count} Patients**

    **CRITICAL REQUIREMENT: You MUST select EXACTLY {keywords_per_patient} keywords for EVERY patient - no exceptions.**

        **PROFESSIONAL ROLE & EXPERTISE:**
        You are functioning as a Board-Certified Clinical Pharmacist and Internal Medicine Physician with specialized expertise in:
        - Clinical pharmacology and medication therapy management
        - Chronic disease state management and comorbidity assessment
        - Evidence-based therapeutic decision making
        - Comprehensive medication review and clinical correlation
        - Risk stratification and preventive care protocols

        **CLINICAL COMPETENCIES:**
        As an expert clinician, you possess advanced knowledge in:
        - Drug-disease interactions and therapeutic indications
        - Pathophysiology of chronic conditions and their interconnections
        - Clinical guidelines for disease management (ADA, AHA, ESC, etc.)
        - Pharmacokinetics, pharmacodynamics, and medication optimization
        - Risk factor identification and clinical prediction models

        **Objective:**
        You are a specialized AI assistant processing {patient_count} patients simultaneously. For each patient, you MUST select exactly {keywords_per_patient} keywords from the provided campaign keywords list.

        **MANDATORY RULES - CRITICAL:**
        1. EXACTLY {keywords_per_patient} keywords per patient - this is non-negotiable and mandatory
        2. ONLY use keywords from the provided campaign keywords list - ABSOLUTELY NO EXCEPTIONS
        3. DO NOT create, invent, or use any keywords not in the campaign keywords list
        4. If a keyword is not in the campaign keywords list, DO NOT USE IT - find alternatives from the list
        5. Base selections on specific patient data elements (conditions, medications, symptoms, procedures)
        6. If fewer than {keywords_per_patient} direct matches exist, select the most relevant available keywords from the campaign list
        7. Do NOT consider medication side effects or hypothetical conditions
        8. Provide SPECIFIC, data-driven reasoning citing exact patient data elements

        **KEYWORD VALIDATION - ABSOLUTELY CRITICAL:**
        - Before selecting ANY keyword, verify it exists in the campaign keywords list
        - Do NOT use medical terms like "GERD", "hyperlipidemia", "diabetes" unless they appear in the campaign keywords list
        - Do NOT use abbreviations or medical codes unless they are in the campaign keywords list
        - ONLY select from the exact keywords provided in the campaign keywords list

        **REASONING REQUIREMENTS - CRITICAL:**
        - NEVER use vague terms like "common symptom", "broadly relevant", "possible condition", "general health"
        - ALWAYS cite specific data from the patient record (exact medication names, diagnosed conditions, documented symptoms, procedures)
        - If selecting a keyword without direct evidence, explain the clinical logic based on patient's documented conditions/medications
        - Format: "Patient has [specific condition/medication/symptom from data] which relates to [keyword]"
        - For medications: "Patient prescribed [exact medication name] for [documented condition/indication]"

        **Selection Strategy (in order of priority):**
        1. Keywords matching documented diagnoses/conditions (cite exact condition names)
        2. Keywords matching prescribed medications (cite exact medication names and indications)
        3. Keywords matching documented symptoms/procedures (cite specific findings)
        4. Keywords clinically related to documented conditions (explain the clinical connection)
        5. Most relevant remaining keywords (explain selection logic based on patient profile)

        **FORBIDDEN Keyword Examples:**
        ❌ Using "GERD" if not in campaign keywords list
        ❌ Using "hyperlipidemia" if not in campaign keywords list
        ❌ Using "diabetes" if not in campaign keywords list
        ❌ Using any medical term not in the campaign keywords list

        **FORBIDDEN Reasoning Examples:**
        ❌ "Common symptom"
        ❌ "Broadly relevant symptom"
        ❌ "Possible underlying condition"
        ❌ "General health concern"
        ❌ "Selected to cover broad range"

        **REQUIRED Reasoning Examples:**
        ✅ "Patient diagnosed with [condition] documented in medical history, relates to [campaign_keyword]"
        ✅ "Patient prescribed [medication_name] for [condition], relates to [campaign_keyword]"
        ✅ "Patient has documented [symptom/finding], relates to [campaign_keyword]"
        ✅ "Patient's documented [condition] clinically relates to [campaign_keyword]"

    **CRITICAL REMINDER:**
    Every keyword you select MUST be found in the campaign keywords list. Do not use medical terms, abbreviations, or conditions unless they appear exactly in the campaign keywords list.

    **Required JSON Structure (EXACTLY {keywords_per_patient} keywords per patient):**
    {{
        "1": {{
            "keywords": ["diabetes", "hypertension", "heart_disease", "cholesterol", "obesity", "kidney_disease", "stroke"],
            "reasoning": {{
                "diabetes": "Patient diagnosed with Type 2 Diabetes Mellitus documented in medical history",
                "hypertension": "Patient prescribed lisinopril 10mg for documented high blood pressure (BP 150/90)",
                "heart_disease": "Patient has documented coronary artery disease with stent placement procedure",
                "cholesterol": "Patient prescribed atorvastatin 20mg for hyperlipidemia management",
                "obesity": "Patient BMI 32.5 documented, classified as obese",
                "kidney_disease": "Patient's diabetes and hypertension increase chronic kidney disease risk",
                "stroke": "Patient's documented atrial fibrillation increases stroke risk, on warfarin therapy"
            }}
        }},
        "2": {{
            "keywords": ["asthma", "allergies", "copd", "pneumonia", "bronchitis", "lung_cancer", "sleep_apnea"],
            "reasoning": {{
                "asthma": "Patient diagnosed with asthma, prescribed albuterol inhaler for symptom control",
                "allergies": "Patient has documented seasonal allergies, prescribed cetirizine",
                "copd": "Patient diagnosed with COPD, documented smoking history 20 pack-years",
                "pneumonia": "Patient hospitalized for pneumonia in past year as documented",
                "bronchitis": "Patient's COPD increases susceptibility to chronic bronchitis episodes",
                "lung_cancer": "Patient's smoking history and COPD increase lung cancer screening relevance",
                "sleep_apnea": "Patient prescribed CPAP machine for documented obstructive sleep apnea"
            }}
        }}
    }}

    **CRITICAL VALIDATION CHECKS:**
    Before submitting your response, verify:
    1. EVERY patient has exactly {keywords_per_patient} keywords in their keywords array
    2. EVERY keyword is from the provided campaign keywords list (check each one!)
    3. EVERY reasoning statement cites specific patient data (no vague terms)
    4. EVERY reasoning explains WHY the keyword was selected based on patient data

    **FINAL KEYWORD & REASONING VERIFICATION:**
    For each keyword you select, ask yourself:
    - Is this keyword in the campaign keywords list? (If NO, do not use it!)
    - Can I find this exact keyword in the provided campaign keywords list?
    - Am I using medical terms that are not in the campaign keywords list?
    - Does the reasoning cite specific patient data and avoid all forbidden vague terms?

    **REASONING QUALITY CONTROL:**
    - Review each reasoning statement - does it cite specific patient data?
    - Replace any vague terms with specific medical information from patient record
    - Ensure medication names, condition names, and clinical values are mentioned when relevant
    - If no direct connection exists, explain the clinical logic clearly

    **Input Data:**
    Patient Data ({patient_count} patients):
    {patient_data_json}

    REMEMBER: Only use keywords that appear in the campaign keywords list above. Do not use medical terms like "GERD", "hyperlipidemia", "diabetes" unless they appear in the campaign keywords list.

    Generate the complete JSON response ensuring EXACTLY {keywords_per_patient} keywords with SPECIFIC reasoning for ALL {patient_count} patients:
    """

PROMPT_HEADER_KEYWORDS_ONLY = """
    ████████████████████████████████████████████████████████████████████████████████████
    **STOP: ONLY USE THE FOLLOWING CAMPAIGN KEYWORDS. DO NOT USE ANY OTHER TERMS.**
    ████████████████████████████████████████████████████████████████████████████████████

    **CAMPAIGN KEYWORDS (the ONLY valid options, numbered for reference):**
    {campaign_list}

    (You may also refer to the keywords as a comma-separated list: {campaign_csv})

    **AI Agent Task: Campaign Keyword Selection for {patient_count} Patients**

    **CRITICAL REQUIREMENT: You MUST select EXACTLY {keywords_per_patient} keywords for EVERY patient - no exceptions.**

        **PROFESSIONAL ROLE & EXPERTISE:**
        You are functioning as a Board-Certified Clinical Pharmacist and Internal Medicine Physician with specialized expertise in:
        - Clinical pharmacology and medication therapy management
        - Chronic disease state management and comorbidity assessment
        - Evidence-based therapeutic decision making
        - Comprehensive medication review and clinical correlation
        - Risk stratification and preventive care protocols

        **CLINICAL COMPETENCIES:**
        As an expert clinician, you possess advanced knowledge in:
        - Drug-disease interactions and therapeutic indications
        - Pathophysiology of chronic conditions and their interconnections
        - Clinical guidelines for disease management (ADA, AHA, ESC, etc.)
        - Pharmacokinetics, pharmacodynamics, and medication optimization
        - Risk factor identification and clinical prediction models

        **Objective:**
        You are a specialized AI assistant processing {patient_count} patients simultaneously. For each patient, you MUST select exactly {keywords_per_patient} keywords from the provided campaign keywords list.

        **MANDATORY RULES - CRITICAL:**
        1. EXACTLY {keywords_per_patient} keywords per patient - this is non-negotiable and mandatory
        2. ONLY use keywords from the provided campaign keywords list - ABSOLUTELY NO EXCEPTIONS
        3. DO NOT create, invent, or use any keywords not in the campaign keywords list
        4. If a keyword is not in the campaign keywords list, DO NOT USE IT - find alternatives from the list
        5. Base selections on specific patient data elements (conditions, medications, symptoms, procedures)
        6. If fewer than {keywords_per_patient} direct matches exist, select the most relevant available keywords from the campaign list
        7. Do NOT consider medication side effects or hypothetical conditions

        **KEYWORD VALIDATION - ABSOLUTELY CRITICAL:**
        - Before selecting ANY keyword, verify it exists in the campaign keywords list
        - Do NOT use medical terms like "GERD", "hyperlipidemia", "diabetes" unless they appear in the campaign keywords list
        - Do NOT use abbreviations or medical codes unless they are in the campaign keywords list
        - ONLY select from the exact keywords provided in the campaign keywords list

        **Selection Strategy (in order of priority):**
        1. Keywords matching documented diagnoses/conditions
        2. Keywords matching prescribed medications
        3. Keywords matching documented symptoms/procedures
        4. Keywords clinically related to documented conditions
        5. Most relevant remaining keywords from the campaign list

        **FORBIDDEN Keyword Examples:**
        ❌ Using "GERD" if not in campaign keywords list
        ❌ Using "hyperlipidemia" if not in campaign keywords list
        ❌ Using "diabetes" if not in campaign keywords list
        ❌ Using any medical term not in the campaign keywords list

    **CRITICAL REMINDER:**
    Every keyword you select MUST be found in the campaign keywords list. Do not use medical terms, abbreviations, or conditions unless they appear exactly in the campaign keywords list.

    **Required JSON Structure (EXACTLY {keywords_per_patient} keywords per patient, NO reasoning required):**
    {{
        "1": {{
            "keywords": ["diabetes", "hypertension", "heart_disease", "cholesterol", "obesity", "kidney_disease", "stroke"]
        }},
        "2": {{
            "keywords": ["asthma", "allergies", "copd", "pneumonia", "bronchitis", "lung_cancer", "sleep_apnea"]
        }}
    }}

    **CRITICAL VALIDATION CHECKS:**
    Before submitting your response, verify:
    1. EVERY patient has exactly {keywords_per_patient} keywords in their keywords array
    2. EVERY keyword is from the provided campaign keywords list (check each one!)
    3. NO reasoning field is included (keywords only)

    **FINAL KEYWORD VERIFICATION:**
    For each keyword you select, ask yourself:
    - Is this keyword in the campaign keywords list? (If NO, do not use it!)
    - Can I find this exact keyword in the provided campaign keywords list?
    - Am I using medical terms that are not in the campaign keywords list?

    **Input Data:**
    Patient Data ({patient_count} patients):
    {patient_data_json}

    REMEMBER: Only use keywords that appear in the campaign keywords list above. Do not use medical terms like "GERD", "hyperlipidemia", "diabetes" unless they appear in the campaign keywords list.

    Generate the complete JSON response ensuring EXACTLY {keywords_per_patient} keywords (NO reasoning) for ALL {patient_count} patients:
    """

def build_prompt(patient_json, campaign, format_hint=""):
    """
    Build a comprehensive prompt for LLM keyword generation.

    Creates a detailed prompt that includes:
    - Strict instructions to use only campaign keywords
    - Numbered list of valid campaign keywords
    - Quality requirements for reasoning (if enabled)
    - Patient clinical data in JSON format
    - Examples of valid and invalid outputs

    The prompt is designed to maximize compliance with keyword restrictions
    and ensure high-quality, specific clinical reasoning when required.

    Args:
        patient_json (Dict[str, Any]): Dictionary mapping patient IDs to clinical data
        campaign (List[str]): List of valid campaign keywords
        format_hint (str, optional): Additional formatting instructions. Defaults to "".

    Returns:
        str: Complete prompt ready for LLM processing

    Example:
        >>> patient_data = {"123": {"Problems": [{"ProblemName": "Diabetes"}]}}
        >>> keywords = ["diabetes", "hypertension", "asthma"]
        >>> prompt = build_prompt(patient_data, keywords)
        >>> len(prompt) > 1000  # Comprehensive prompt
        True
    """
    logger.info(f"Building prompt for {len(patient_json)} patients and {len(campaign)} campaign keywords")
    logger.info(f"Reasoning required: {config.REASONING_REQUIRED}")
    logger.debug(f"Patient data size: {len(json.dumps(patient_json, default=str))} characters")

    # Create numbered list of campaign keywords for easy reference
    clist = "\n".join(f"{i+1}. {kw}" for i, kw in enumerate(campaign))

    # Choose the appropriate prompt template based on reasoning requirement
    if config.REASONING_REQUIRED:
        template = PROMPT_HEADER_WITH_REASONING
        logger.debug("Using prompt template with reasoning")
    else:
        template = PROMPT_HEADER_KEYWORDS_ONLY
        logger.debug("Using keywords-only prompt template")

    # Format the main prompt template with campaign keywords and patient data
    from datetime import datetime, date

    def datetime_serializer(obj):
        """JSON serializer for datetime and date objects"""
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    # Pre-serialize patient data to handle datetime objects
    try:
        patient_data_json = json.dumps(patient_json, separators=(',', ':'), default=datetime_serializer)
        logger.debug(f"Patient data serialized successfully: {len(patient_data_json)} characters")
    except Exception as e:
        logger.error(f"Failed to serialize patient data: {e}")
        raise

    # Format the template with all required variables
    header = template.format(
        campaign_list=clist,
        campaign_csv=", ".join(campaign),
        patient_count=len(patient_json),
        patient_data_json=patient_data_json,
        keywords_per_patient=config.KEYWORDS_PER_PATIENT
    )

    # Assemble the complete prompt
    prompt = (
        f"{header}\n\n"  # Detailed header with instructions and keywords
        f"AI Agent Task: Select EXACTLY {config.KEYWORDS_PER_PATIENT} keywords for each patient."  # Core task
        f"\n\nInput Data ({len(patient_json)} patients):\n{patient_data_json}\n\n"
    )

    # Add optional formatting hint
    if format_hint:
        prompt += format_hint + "\n"
    
    # Enhance prompt with quality examples if available
    try:
        from src.llm_improvements import prompt_builder
        prompt = prompt_builder.add_quality_examples(prompt, campaign)
        logger.debug("Added quality examples to prompt")
    except Exception as e:
        logger.warning(f"Could not add quality examples: {e}")

    logger.debug(f"Prompt built - total length: {len(prompt)} characters")
    return prompt

class LLMError(RuntimeError):
    """Custom exception for LLM-related errors."""
    pass

def _retry_llm():
    """
    Create a retry decorator for LLM API calls.
    
    Configures exponential backoff with jitter for robust API calls.
    Retries on any exception up to the configured maximum attempts.

    Returns:
        retry: Tenacity retry decorator configured for LLM calls
    """
    # Lazy load the retry attempts to avoid circular import
    retry_attempts = getattr(config, 'LLM_RETRY_ATTEMPTS', 4)  # Default to 4 if not available
    return retry(
        reraise=True,
        stop=stop_after_attempt(retry_attempts),
        wait=wait_exponential_jitter(initial=1, max=10),
        retry=retry_if_exception_type(Exception),
    )

# @_retry_llm()  # Decorator commented out with function
# def query_gemini(prompt: str) -> str:
#     """
#     Query Google's Gemini LLM with automatic retry logic.

#     Sends a prompt to Gemini 1.5 Flash model and returns the response.
#     Includes automatic retry with exponential backoff for robustness.

#     Args:
#         prompt (str): The prompt to send to Gemini

#     Returns:
#         str: The LLM's response text, stripped of whitespace

#     Raises:
#         EnvironmentError: If GEMINI_API_KEY is not configured
#         Exception: If API call fails after all retries

#     Example:
#         >>> response = query_gemini("Generate keywords for this patient data...")
#         >>> isinstance(response, str)
#         True
#     """
#     import google.generativeai as genai
#     if not config.GEMINI_API_KEY:
#         raise EnvironmentError("GEMINI_API_KEY not set")
    
#     logger.info(f"Sending request to Gemini API (prompt length: {len(prompt)} chars)")
#     start_time = time.time()

#     # Configure Gemini client and model
#     genai.configure(api_key=config.GEMINI_API_KEY)
#     model = genai.GenerativeModel("gemini-1.5-flash-latest")

#     # Generate content with configured temperature
#     resp = model.generate_content(prompt, generation_config={"temperature": config.LLM_TEMPERATURE})
    
#     response_time = time.time() - start_time
#     logger.info(f"Gemini API response received in {response_time:.2f}s")
    
#     # Log token usage if available
#     try:
#         if hasattr(resp, 'usage_metadata'):
#             logger.info(f"Token usage - Input: {resp.usage_metadata.prompt_token_count}, "
#                        f"Output: {resp.usage_metadata.candidates_token_count}, "
#                        f"Total: {resp.usage_metadata.total_token_count}")
#     except Exception as e:
#         logger.debug(f"Could not log token usage: {e}")
    
#     return resp.text.strip()

def query_gemini(prompt: str) -> str:
    """
    Query Google's Gemini LLM through the API routing service.

    Sends a prompt to the API server which routes it to Gemini 1.5 Flash model.
    Includes JWT authentication and automatic retry logic for robustness.

    Args:
        prompt (str): The prompt to send to Gemini

    Returns:
        str: The LLM's response text, stripped of whitespace

    Raises:
        EnvironmentError: If API_SERVER_URL or CLIENT_JWT is not configured
        Exception: If API call fails after all retries

    Example:
        >>> response = query_gemini("Generate keywords for this patient data...")
        >>> isinstance(response, str)
        True
    """
    import requests
    import time

    # Check if we should use API server or direct Gemini call
    if config.API_SERVER_URL and config.CLIENT_JWT:
        # Use API server routing
        return _query_gemini_via_api_server(prompt)
    elif config.GEMINI_API_KEY:
        # Fallback to direct Gemini call
        logger.warning("API server not configured, falling back to direct Gemini API call")
        return _query_gemini_direct(prompt)
    else:
        raise EnvironmentError("Neither API server (API_SERVER_URL + CLIENT_JWT) nor direct Gemini API (GEMINI_API_KEY) is configured")

def _query_gemini_via_api_server(prompt: str) -> str:
    """
    Query Gemini through the API routing service.
    
    Args:
        prompt (str): The prompt to send to Gemini
        
    Returns:
        str: The LLM's response text, stripped of whitespace
    """
    import requests
    import time

    if not config.API_SERVER_URL or not config.CLIENT_JWT:
        raise EnvironmentError("API_SERVER_URL and CLIENT_JWT must be configured for API server routing")

    logger.info(f"Sending request to API server for Gemini (prompt length: {len(prompt)} chars)")
    start_time = time.time()

    headers = {
        "Authorization": f"Bearer {config.CLIENT_JWT}",
        "Content-Type": "application/json"
    }

    payload = {
        "prompt": prompt,
        "temperature": config.LLM_TEMPERATURE,
        "client_id": config.CLIENT_ID,
        "max_output_tokens": config.MAX_OUTPUT_TOKENS
    }

    try:
        response = requests.post(
            f"{config.API_SERVER_URL}/gemini/query", 
            json=payload, 
            headers=headers,
            timeout=300  # 5 minute timeout for large requests
        )

        if response.status_code == 401:
            raise EnvironmentError("JWT token expired or invalid. Please refresh your authentication.")
        elif response.status_code == 403:
            raise EnvironmentError("Access denied. Check your client permissions.")
        elif response.status_code == 413:
            raise RuntimeError("Request too large. Reduce prompt size or batch size.")
        elif response.status_code == 429:
            raise RuntimeError("Rate limit exceeded. Please wait before retrying.")
        elif response.status_code != 200:
            logger.error(f"API server error: {response.status_code} - {response.text}")
            raise RuntimeError(f"API server returned error {response.status_code}: {response.text}")

        result = response.json()
        response_time = time.time() - start_time
        
        logger.info(f"Gemini response from API server received in {response_time:.2f}s")
        
        # Log token usage from API server response
        token_usage = result.get("token_usage", {})
        if token_usage:
            logger.info(f"Token usage - Input: {token_usage.get('input_tokens', 0)}, "
                       f"Output: {token_usage.get('output_tokens', 0)}, "
                       f"Total: {token_usage.get('total_tokens', 0)}")
            logger.info(f"Estimated cost: ${result.get('estimated_cost_usd', 0):.6f} USD")
        
        logger.info(f"Request ID: {result.get('request_id', 'unknown')}")
        
        return result.get("response", "").strip()

    except requests.exceptions.Timeout:
        raise RuntimeError("API server request timed out. The request may be too large.")
    except requests.exceptions.ConnectionError:
        raise RuntimeError("Could not connect to API server. Check if the server is running.")
    except requests.exceptions.RequestException as e:
        raise RuntimeError(f"HTTP request failed: {e}")
    except Exception as e:
        raise RuntimeError(f"Unexpected error calling API server: {e}")

def _query_gemini_direct(prompt: str) -> str:
    """
    Query Google's Gemini LLM directly (fallback method).

    Args:
        prompt (str): The prompt to send to Gemini

    Returns:
        str: The LLM's response text, stripped of whitespace
    """
    import google.generativeai as genai
    import time

    if not config.GEMINI_API_KEY:
        raise EnvironmentError("GEMINI_API_KEY not set")

    logger.info(f"Sending request to Gemini API directly (prompt length: {len(prompt)} chars)")
    start_time = time.time()

    # Configure Gemini client and model
    genai.configure(api_key=config.GEMINI_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash-latest")

    # Generate content with configured temperature
    resp = model.generate_content(prompt, generation_config={"temperature": config.LLM_TEMPERATURE})
    
    response_time = time.time() - start_time
    logger.info(f"Gemini API response received in {response_time:.2f}s")
    
    # Log token usage if available
    try:
        if hasattr(resp, 'usage_metadata'):
            logger.info(f"Token usage - Input: {resp.usage_metadata.prompt_token_count}, "
                       f"Output: {resp.usage_metadata.candidates_token_count}, "
                       f"Total: {resp.usage_metadata.total_token_count}")
    except Exception as e:
        logger.debug(f"Could not log token usage: {e}")
    
    return resp.text.strip()




def check_run_limits(inp_tok: int, out_tok_est: int):
    """
    Check if the planned Gemini LLM call would exceed configured limits.

    Validates both token count and estimated cost against configured limits
    to prevent expensive or oversized API calls. Issues warnings when approaching
    limits and raises errors if limits would be exceeded.

    Args:
        inp_tok (int): Number of input tokens
        out_tok_est (int): Estimated number of output tokens

    Raises:
        RuntimeError: If token limit or cost limit would be exceeded

    Example:
        >>> check_run_limits(50000, 8000)  # Should pass
        >>> check_run_limits(1000000, 8000)  # Raises RuntimeError
    """
    total_tokens = inp_tok + out_tok_est

    # Calculate estimated cost using Gemini pricing
    pricing = config.GEMINI_PRICING
    est_cost = (inp_tok / 1000) * pricing["input"] + (out_tok_est / 1000) * pricing["output"]

    # # Log token and cost information
    # logger.info(f"Token usage: {inp_tok:,} input + {out_tok_est:,} estimated output = {total_tokens:,} total")
    # logger.info(f"Estimated cost: ${est_cost:.4f} USD")

    # Check individual input token limit
    if inp_tok > config.MAX_INPUT_TOKENS:
        raise RuntimeError(f"Input token limit exceeded: {inp_tok:,} > {config.MAX_INPUT_TOKENS:,}")

    # Check individual output token limit
    if out_tok_est > config.MAX_OUTPUT_TOKENS:
        raise RuntimeError(f"Output token limit exceeded: {out_tok_est:,} > {config.MAX_OUTPUT_TOKENS:,}")

    # Check total token limit
    if total_tokens > config.MAX_TOKENS_PER_RUN:
        raise RuntimeError(f"Total token limit exceeded: {total_tokens:,} > {config.MAX_TOKENS_PER_RUN:,}")

    # Check cost limit
    if est_cost > config.MAX_COST_USD:
        raise RuntimeError(f"Cost limit exceeded: ${est_cost:.4f} > ${config.MAX_COST_USD:.2f}")

    # Issue warnings when approaching limits
    token_usage_pct = total_tokens / config.MAX_TOKENS_PER_RUN
    cost_usage_pct = est_cost / config.MAX_COST_USD

    if token_usage_pct >= config.TOKEN_WARNING_THRESHOLD:
        logger.warning(f"High token usage: {token_usage_pct:.1%} of limit ({total_tokens:,}/{config.MAX_TOKENS_PER_RUN:,})")

    if cost_usage_pct >= config.COST_WARNING_THRESHOLD:
        logger.warning(f"High cost usage: {cost_usage_pct:.1%} of limit (${est_cost:.4f}/${config.MAX_COST_USD:.2f})")

    # Log input token usage specifically
    input_usage_pct = inp_tok / config.MAX_INPUT_TOKENS
    if input_usage_pct >= config.TOKEN_WARNING_THRESHOLD:
        logger.warning(f"High input token usage: {input_usage_pct:.1%} of limit ({inp_tok:,}/{config.MAX_INPUT_TOKENS:,})")

    # Log output token usage specifically
    output_usage_pct = out_tok_est / config.MAX_OUTPUT_TOKENS
    if output_usage_pct >= config.TOKEN_WARNING_THRESHOLD:
        logger.warning(f"High output token usage: {output_usage_pct:.1%} of limit ({out_tok_est:,}/{config.MAX_OUTPUT_TOKENS:,})")

    logger.debug(f"Limits check passed - Token usage: {token_usage_pct:.1%}, Cost usage: {cost_usage_pct:.1%}")
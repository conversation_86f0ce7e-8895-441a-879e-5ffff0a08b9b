"""
Source package for Campaign Keywords Pipeline.

This package contains the core modules for the pipeline:
- pipeline: Main orchestration logic
- db: Database operations
- llm: LLM integration
- config_loader: Configuration management
- health_check: Health monitoring API
- logging_config: Logging with rotation
- db_pool: Connection pooling
- llm_improvements: LLM response quality enhancements
- performance_optimizations: Performance and resource optimizations
"""

__version__ = "0.3.0"  # Updated for LLM and performance improvements

# Import main components for easier access
from . import pipeline
from . import db
from . import llm
from . import config_loader
from . import health_check
from . import logging_config
from . import db_pool
from . import llm_improvements
from . import performance_optimizations

__all__ = [
    "pipeline",
    "db", 
    "llm",
    "config_loader",
    "health_check",
    "logging_config",
    "db_pool",
    "llm_improvements",
    "performance_optimizations"
]
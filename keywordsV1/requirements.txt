# Core dependencies for Campaign Keywords Pipeline
# AI/ML Libraries
google-generativeai>=0.3.0    # Google Gemini API client for LLM integration
langfuse>=2.0.0               # Observability and monitoring for LLM applications

# Database connectivity
pyodbc>=4.0.39                # SQL Server ODBC driver for database operations
sqlalchemy>=2.0.0             # ORM for database operations

# Data processing
pandas>=2.0.0                 # Data manipulation and analysis

# Utility libraries
tenacity>=8.2.0               # Retry logic with exponential backoff for API calls
tiktoken>=0.5.0               # Token counting for accurate cost estimation

# API Server Dependencies (NEW)
fastapi>=0.104.0              # Modern, fast web framework for building APIs
uvicorn>=0.24.0               # ASGI server for FastAPI
pyjwt>=2.8.0                  # JWT token handling for authentication
python-multipart>=0.0.6       # For handling form data and file uploads
python-jose[cryptography]>=3.3.0  # JWT token signing and verification

# Health monitoring and operations
flask>=3.0.0                  # Web framework for health check API
psutil>=5.9.0                 # System and process utilities for resource monitoring

# Testing
requests>=2.31.0              # HTTP library for testing health check endpoints

# Utilities
tabulate>=0.9.0               # Table formatting for database schema display

# Development dependencies (optional)
# Uncomment the following lines for development environment:
# pytest>=7.0.0               # Testing framework
# pytest-cov>=4.0.0           # Test coverage reporting
# black>=23.0.0               # Code formatting
# flake8>=6.0.0               # Code linting
# mypy>=1.0.0                 # Static type checking
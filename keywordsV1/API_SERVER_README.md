# Gemini API Routing Service

A FastAPI-based routing service that provides centralized access to Google's Gemini LLM with JWT authentication, request tracking, and comprehensive monitoring.

## 🎯 Overview

This service acts as a secure gateway between your clients and the Gemini API, providing:

- **🔐 JWT Authentication**: Secure token-based authentication for all clients
- **📊 Request Tracking**: Complete logging of all requests/responses in SQL Server
- **💰 Cost Monitoring**: Real-time token usage and cost tracking
- **🔄 Centralized Management**: Single point of control for all Gemini API access
- **📈 Usage Analytics**: Per-client statistics and usage monitoring
- **🛡️ Security**: API key isolation - clients never see the actual Gemini API key

## 🏗️ Architecture

```
┌─────────────┐    JWT Auth    ┌─────────────────┐    API Key    ┌─────────────┐
│   Client    ├──────────────→ │  API Routing    ├─────────────→ │   Gemini    │
│ Application │                │     Service     │               │     API     │
└─────────────┘                └─────────────────┘               └─────────────┘
                                        │
                                        ▼
                               ┌─────────────────┐
                               │   SQL Server    │
                               │ (Request Logs)  │
                               └─────────────────┘
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Environment Variables

**Windows (PowerShell):**
```powershell
$env:GEMINI_API_KEY = "your-gemini-api-key"
$env:JWT_SECRET_KEY = "your-super-secret-jwt-key"
```

**Windows (Command Prompt):**
```cmd
set GEMINI_API_KEY=your-gemini-api-key
set JWT_SECRET_KEY=your-super-secret-jwt-key
```

### 3. Start the Server

**Option A: Use the startup scripts**
```bash
# Windows
.\start_api_server.bat
# or
.\start_api_server.ps1
```

**Option B: Direct Python execution**
```bash
python api_server.py
```

### 4. Verify Installation

Visit `http://localhost:8000/docs` to see the interactive API documentation.

Check health: `http://localhost:8000/health`

## 🔑 Client Authentication

### Step 1: Get a JWT Token

Use the authentication utility:

```bash
python client_auth.py --client-id "my_client" --secret "demo-secret-key" --test
```

Or make a direct API call:

```bash
curl -X POST "http://localhost:8000/auth/token?client_id=my_client&secret_key=demo-secret-key"
```

### Step 2: Configure Your Client

The authentication utility will automatically update your `config.py` file, or you can manually set:

```python
# config.py
API_SERVER_URL = "http://localhost:8000"
CLIENT_JWT = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
CLIENT_ID = "my_client"
```

## 📡 API Endpoints

### Authentication

#### `POST /auth/token`
Get a JWT token for API access.

**Parameters:**
- `client_id` (string): Your client identifier
- `secret_key` (string): Authentication secret

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Gemini API

#### `POST /gemini/query`
Route a request to Gemini API.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "prompt": "Your prompt text here",
  "temperature": 0.2,
  "client_id": "my_client",
  "max_output_tokens": 8192
}
```

**Response:**
```json
{
  "response": "Gemini's response text",
  "token_usage": {
    "input_tokens": 150,
    "output_tokens": 75,
    "total_tokens": 225
  },
  "response_time_ms": 1250,
  "estimated_cost_usd": 0.000125,
  "request_id": "uuid-string"
}
```

### Monitoring

#### `GET /health`
Check service health and Gemini API connectivity.

#### `GET /stats/{client_id}`
Get usage statistics for a specific client (requires authentication).

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GEMINI_API_KEY` | Required | Your Google Gemini API key |
| `JWT_SECRET_KEY` | Required | Secret key for JWT signing |
| `API_SERVER_HOST` | `0.0.0.0` | Server bind address |
| `API_SERVER_PORT` | `8000` | Server port |
| `DB_DRIVER` | `ODBC Driver 18 for SQL Server` | Database driver |
| `DB_SERVER` | `************` | Database server |
| `DB_NAME` | `CAEnterprise` | Database name |
| `DB_USERNAME` | `devAdmin` | Database username |
| `DB_PASSWORD` | Required | Database password |
| `MAX_REQUESTS_PER_MINUTE` | `60` | Rate limit per client |
| `MAX_TOKENS_PER_REQUEST` | `100000` | Maximum tokens per request |

### Database Schema

The service automatically creates the `api_request_logs` table:

```sql
CREATE TABLE api_request_logs (
    id INTEGER PRIMARY KEY IDENTITY,
    client_id VARCHAR(100) NOT NULL,
    request_id VARCHAR(36) NOT NULL UNIQUE,
    endpoint VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    prompt_hash VARCHAR(64) NOT NULL,
    prompt_length INTEGER NOT NULL,
    response_hash VARCHAR(64),
    response_length INTEGER,
    input_tokens INTEGER,
    output_tokens INTEGER,
    total_tokens INTEGER,
    estimated_cost_usd FLOAT,
    response_time_ms INTEGER,
    status_code INTEGER NOT NULL,
    error_message TEXT,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45)
);
```

## 💻 Client Integration

### Python Client Example

```python
import requests
import config

def query_gemini_via_api(prompt: str) -> str:
    """Query Gemini through the API routing service."""
    
    headers = {
        "Authorization": f"Bearer {config.CLIENT_JWT}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "prompt": prompt,
        "temperature": config.LLM_TEMPERATURE,
        "client_id": config.CLIENT_ID
    }
    
    response = requests.post(
        f"{config.API_SERVER_URL}/gemini/query",
        json=payload,
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["response"]
    else:
        raise Exception(f"API error: {response.status_code} - {response.text}")

# Usage
response = query_gemini_via_api("Generate keywords for patient data...")
print(response)
```

### Existing Code Migration

Your existing `query_gemini()` function will automatically use the API server if configured:

```python
# Your existing code works unchanged!
from src.llm import query_gemini

response = query_gemini("Your prompt here")
```

The function automatically detects if API server credentials are configured and routes accordingly.

## 📊 Monitoring & Analytics

### Request Tracking

All requests are logged with:
- **Privacy Protection**: Prompts and responses are SHA-256 hashed
- **Token Usage**: Input, output, and total token counts
- **Cost Tracking**: Real-time cost estimation
- **Performance Metrics**: Response times and error rates
- **Client Attribution**: Per-client usage tracking

### Usage Statistics

Query client statistics:

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     "http://localhost:8000/stats/my_client"
```

Response:
```json
{
  "client_id": "my_client",
  "total_requests": 150,
  "total_tokens": 45000,
  "total_cost_usd": 12.50,
  "avg_response_time_ms": 1200
}
```

## 🛡️ Security Features

### JWT Authentication
- **Token Expiration**: Configurable token lifetime (default: 1 hour)
- **Secure Signing**: HMAC-SHA256 algorithm
- **Client Isolation**: Per-client token validation

### Privacy Protection
- **Content Hashing**: Prompts and responses are hashed, not stored in plaintext
- **API Key Isolation**: Clients never see the actual Gemini API key
- **Access Control**: Client-specific permissions and rate limiting

### Error Handling
- **Graceful Degradation**: Automatic fallback to direct Gemini API if configured
- **Detailed Logging**: Comprehensive error tracking and monitoring
- **Rate Limiting**: Configurable per-client request limits

## 🔍 Troubleshooting

### Common Issues

**1. "Could not connect to API server"**
- Verify the server is running: `curl http://localhost:8000/health`
- Check firewall settings and port availability

**2. "JWT token expired or invalid"**
- Re-authenticate: `python client_auth.py --client-id "your_client" --secret "demo-secret-key"`
- Verify JWT_SECRET_KEY matches between client and server

**3. "Database connection failed"**
- Verify SQL Server is accessible
- Check database credentials and connection string
- Ensure ODBC driver is installed

**4. "Gemini API authentication error"**
- Verify GEMINI_API_KEY is correct and active
- Check API quota and billing status

### Debug Mode

Start the server with debug logging:

```python
# In api_server.py, change:
logging.basicConfig(level=logging.DEBUG)
```

### Health Checks

Monitor service health:

```bash
# Basic health check
curl http://localhost:8000/health

# Detailed health with auth
curl -H "Authorization: Bearer <token>" \
     http://localhost:8000/health
```

## 🚀 Production Deployment

### Security Checklist

- [ ] Change default JWT_SECRET_KEY
- [ ] Use environment variables for all secrets
- [ ] Configure CORS origins appropriately
- [ ] Set up HTTPS/TLS termination
- [ ] Implement proper client authentication database
- [ ] Configure rate limiting per client requirements
- [ ] Set up monitoring and alerting
- [ ] Regular database maintenance and archiving

### Scaling Considerations

- **Load Balancing**: Multiple API server instances behind a load balancer
- **Database**: Connection pooling and read replicas for analytics
- **Caching**: Redis for JWT token validation and rate limiting
- **Monitoring**: Prometheus/Grafana for metrics and alerting

### Example Production Configuration

```bash
# Production environment variables
export GEMINI_API_KEY="your-production-api-key"
export JWT_SECRET_KEY="your-production-jwt-secret-256-bit"
export API_SERVER_HOST="0.0.0.0"
export API_SERVER_PORT="8000"
export MAX_REQUESTS_PER_MINUTE="1000"
export DB_SERVER="your-production-db-server"
export DB_NAME="production_db"
# ... other production settings
```

## 📝 API Documentation

Once the server is running, visit:
- **Interactive Docs**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review server logs for detailed error messages
3. Test with the authentication utility: `python client_auth.py --test`
4. Verify health endpoint: `curl http://localhost:8000/health`

## 📄 License

This project is part of the Campaign Keywords pipeline system. 
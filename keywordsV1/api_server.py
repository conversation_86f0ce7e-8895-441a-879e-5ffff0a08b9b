"""
API Routing Service for Gemini LLM Integration

This FastAPI service acts as a central routing layer between clients and the Gemini API.
It provides:
- JWT-based authentication for secure client access
- Request/response tracking in a database for monitoring and billing
- Centralized Gemini API key management
- Rate limiting and error handling
- Token usage and cost tracking

The service ensures that clients never directly access the Gemini API while maintaining
full observability and control over LLM usage across the organization.
"""

import os
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
import hashlib
import json

# FastAPI and dependencies
from fastapi import FastAPI, HTTPException, Depends, Request, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# JWT handling
import jwt
from jose import JWTError, jwt as jose_jwt

# Database
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.mssql import UNIQUEIDEN<PERSON>FIER
import pyodbc

# Gemini API
import google.generativeai as genai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("api_server")

# Configuration from environment variables
class Config:
    # JWT Configuration
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-in-production")
    JWT_ALGORITHM = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 60
    
    # Gemini API
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    
    # Database Configuration
    DB_DRIVER = os.getenv("DB_DRIVER", "ODBC Driver 18 for SQL Server")
    DB_SERVER = os.getenv("DB_SERVER", "************")
    DB_NAME = os.getenv("DB_NAME", "CAEnterprise")
    DB_USERNAME = os.getenv("DB_USERNAME", "devAdmin")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "hOstIR&8l8lWrl=7SlDr")
    DB_ENCRYPT = os.getenv("DB_ENCRYPT", "no")
    DB_TRUST_CERT = os.getenv("DB_TRUST_CERT", "yes")
    
    # Server Configuration
    HOST = os.getenv("API_SERVER_HOST", "0.0.0.0")
    PORT = int(os.getenv("API_SERVER_PORT", "8000"))
    
    # Rate Limiting
    MAX_REQUESTS_PER_MINUTE = int(os.getenv("MAX_REQUESTS_PER_MINUTE", "60"))
    MAX_TOKENS_PER_REQUEST = int(os.getenv("MAX_TOKENS_PER_REQUEST", "100000"))

config = Config()

# Validate required configuration
if not config.GEMINI_API_KEY:
    raise EnvironmentError("GEMINI_API_KEY environment variable is required")

# Configure Gemini API
genai.configure(api_key=config.GEMINI_API_KEY)

# Database Setup
Base = declarative_base()

class APIRequestLog(Base):
    """Database model for tracking API requests and responses."""
    __tablename__ = "api_request_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    client_id = Column(String(100), nullable=False, index=True)
    request_id = Column(String(36), nullable=False, unique=True, index=True)
    
    # Request details
    endpoint = Column(String(100), nullable=False)
    method = Column(String(10), nullable=False)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Prompt and response (hashed for privacy)
    prompt_hash = Column(String(64), nullable=False)  # SHA-256 hash
    prompt_length = Column(Integer, nullable=False)
    response_hash = Column(String(64), nullable=True)  # SHA-256 hash
    response_length = Column(Integer, nullable=True)
    
    # Token usage and costs
    input_tokens = Column(Integer, nullable=True)
    output_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    estimated_cost_usd = Column(Float, nullable=True)
    
    # Performance metrics
    response_time_ms = Column(Integer, nullable=True)
    status_code = Column(Integer, nullable=False)
    
    # Error tracking
    error_message = Column(Text, nullable=True)
    
    # Metadata
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)

# Database connection
def get_database_url():
    """Construct SQL Server connection URL."""
    return (
        f"mssql+pyodbc://{config.DB_USERNAME}:{config.DB_PASSWORD}@"
        f"{config.DB_SERVER}/{config.DB_NAME}?"
        f"driver={config.DB_DRIVER.replace(' ', '+')}&"
        f"Encrypt={config.DB_ENCRYPT}&"
        f"TrustServerCertificate={config.DB_TRUST_CERT}"
    )

# Create database engine and session
try:
    engine = create_engine(get_database_url(), echo=False)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    logger.info("Database connection established and tables created")
except Exception as e:
    logger.error(f"Failed to connect to database: {e}")
    raise

def get_db():
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Pydantic models for request/response
class GeminiQueryRequest(BaseModel):
    """Request model for Gemini API queries."""
    prompt: str = Field(..., min_length=1, max_length=500000, description="The prompt to send to Gemini")
    temperature: float = Field(default=0.2, ge=0.0, le=2.0, description="Temperature for response generation")
    client_id: Optional[str] = Field(default="unknown", description="Client identifier for tracking")
    max_output_tokens: Optional[int] = Field(default=8192, ge=1, le=32768, description="Maximum output tokens")

class TokenUsage(BaseModel):
    """Token usage information."""
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    total_tokens: Optional[int] = None

class GeminiQueryResponse(BaseModel):
    """Response model for Gemini API queries."""
    response: str = Field(..., description="The LLM response text")
    token_usage: TokenUsage = Field(..., description="Token usage statistics")
    response_time_ms: int = Field(..., description="Response time in milliseconds")
    estimated_cost_usd: float = Field(..., description="Estimated cost in USD")
    request_id: str = Field(..., description="Unique request identifier")

class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    timestamp: datetime
    version: str = "1.0.0"
    gemini_api_status: str

# JWT Authentication
security = HTTPBearer()

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, config.JWT_SECRET_KEY, algorithm=config.JWT_ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(
            credentials.credentials, 
            config.JWT_SECRET_KEY, 
            algorithms=[config.JWT_ALGORITHM]
        )
        client_id: str = payload.get("client_id")
        if client_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Utility functions
def hash_text(text: str) -> str:
    """Create SHA-256 hash of text for privacy-preserving logging."""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()

def calculate_cost(input_tokens: int, output_tokens: int) -> float:
    """Calculate estimated cost based on Gemini pricing."""
    # Gemini 1.5 Flash pricing (USD per 1M tokens)
    input_cost_per_1m = 0.25
    output_cost_per_1m = 0.50
    
    input_cost = (input_tokens / 1_000_000) * input_cost_per_1m
    output_cost = (output_tokens / 1_000_000) * output_cost_per_1m
    
    return input_cost + output_cost

def generate_request_id() -> str:
    """Generate a unique request ID."""
    import uuid
    return str(uuid.uuid4())

# FastAPI app initialization
app = FastAPI(
    title="Gemini API Routing Service",
    description="Central routing service for Gemini LLM API with JWT authentication and request tracking",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Routes
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    # Test Gemini API connectivity
    try:
        model = genai.GenerativeModel("gemini-1.5-flash-latest")
        # Quick test with minimal prompt
        test_response = model.generate_content("Hello", generation_config={"max_output_tokens": 10})
        gemini_status = "healthy" if test_response.text else "degraded"
    except Exception as e:
        logger.warning(f"Gemini API health check failed: {e}")
        gemini_status = "unhealthy"
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        gemini_api_status=gemini_status
    )

@app.post("/auth/token")
async def create_token(client_id: str, secret_key: str):
    """Create a JWT token for client authentication."""
    # In production, validate client_id and secret_key against a database
    # For now, we'll use a simple validation
    if secret_key != "demo-secret-key":  # Change this in production
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    
    access_token_expires = timedelta(minutes=config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"client_id": client_id}, 
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@app.post("/gemini/query", response_model=GeminiQueryResponse)
async def route_to_gemini(
    request: GeminiQueryRequest,
    request_obj: Request,
    token_data: Dict[str, Any] = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """
    Route request to Gemini API with full tracking and monitoring.
    
    This endpoint:
    1. Validates JWT authentication
    2. Logs the request details (with hashed content for privacy)
    3. Calls the Gemini API
    4. Tracks token usage and costs
    5. Logs the response details
    6. Returns the response to the client
    """
    start_time = time.time()
    request_id = generate_request_id()
    client_id = token_data.get("client_id", "unknown")
    
    # Extract client information
    user_agent = request_obj.headers.get("user-agent", "")
    client_ip = request_obj.client.host if request_obj.client else "unknown"
    
    logger.info(f"Processing Gemini request {request_id} from client {client_id}")
    
    # Create initial log entry
    log_entry = APIRequestLog(
        client_id=client_id,
        request_id=request_id,
        endpoint="/gemini/query",
        method="POST",
        timestamp=datetime.utcnow(),
        prompt_hash=hash_text(request.prompt),
        prompt_length=len(request.prompt),
        status_code=0,  # Will be updated
        user_agent=user_agent,
        ip_address=client_ip
    )
    
    try:
        # Validate request size
        if len(request.prompt) > config.MAX_TOKENS_PER_REQUEST * 4:  # Rough estimate: 4 chars per token
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Prompt too large. Maximum estimated tokens: {config.MAX_TOKENS_PER_REQUEST}"
            )
        
        # Call Gemini API
        logger.info(f"Sending request to Gemini API (prompt length: {len(request.prompt)} chars)")
        
        model = genai.GenerativeModel("gemini-1.5-flash-latest")
        
        generation_config = {
            "temperature": request.temperature,
            "max_output_tokens": request.max_output_tokens,
        }
        
        response = model.generate_content(
            request.prompt,
            generation_config=generation_config
        )
        
        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Extract token usage
        input_tokens = getattr(response.usage_metadata, 'prompt_token_count', 0) if hasattr(response, 'usage_metadata') else 0
        output_tokens = getattr(response.usage_metadata, 'candidates_token_count', 0) if hasattr(response, 'usage_metadata') else 0
        total_tokens = getattr(response.usage_metadata, 'total_token_count', 0) if hasattr(response, 'usage_metadata') else 0
        
        # Calculate cost
        estimated_cost = calculate_cost(input_tokens, output_tokens)
        
        # Update log entry with success details
        log_entry.response_hash = hash_text(response.text)
        log_entry.response_length = len(response.text)
        log_entry.input_tokens = input_tokens
        log_entry.output_tokens = output_tokens
        log_entry.total_tokens = total_tokens
        log_entry.estimated_cost_usd = estimated_cost
        log_entry.response_time_ms = response_time_ms
        log_entry.status_code = 200
        
        # Save to database
        db.add(log_entry)
        db.commit()
        
        logger.info(f"Gemini request {request_id} completed successfully in {response_time_ms}ms")
        logger.info(f"Token usage - Input: {input_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        logger.info(f"Estimated cost: ${estimated_cost:.6f} USD")
        
        return GeminiQueryResponse(
            response=response.text.strip(),
            token_usage=TokenUsage(
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens
            ),
            response_time_ms=response_time_ms,
            estimated_cost_usd=estimated_cost,
            request_id=request_id
        )
        
    except Exception as e:
        # Calculate response time even for errors
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Update log entry with error details
        log_entry.error_message = str(e)
        log_entry.response_time_ms = response_time_ms
        log_entry.status_code = 500
        
        # Save error to database
        try:
            db.add(log_entry)
            db.commit()
        except Exception as db_error:
            logger.error(f"Failed to save error log to database: {db_error}")
        
        logger.error(f"Gemini request {request_id} failed: {e}")
        
        # Return appropriate HTTP error
        if "quota" in str(e).lower() or "limit" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="API quota exceeded or rate limit hit"
            )
        elif "authentication" in str(e).lower() or "api key" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gemini API authentication error"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error processing Gemini request"
            )

@app.get("/stats/{client_id}")
async def get_client_stats(
    client_id: str,
    token_data: Dict[str, Any] = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get usage statistics for a specific client."""
    # Verify client can access their own stats or is admin
    if token_data.get("client_id") != client_id and token_data.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # Query database for client stats
    from sqlalchemy import func
    
    stats = db.query(
        func.count(APIRequestLog.id).label("total_requests"),
        func.sum(APIRequestLog.total_tokens).label("total_tokens"),
        func.sum(APIRequestLog.estimated_cost_usd).label("total_cost"),
        func.avg(APIRequestLog.response_time_ms).label("avg_response_time")
    ).filter(APIRequestLog.client_id == client_id).first()
    
    return {
        "client_id": client_id,
        "total_requests": stats.total_requests or 0,
        "total_tokens": stats.total_tokens or 0,
        "total_cost_usd": float(stats.total_cost or 0),
        "avg_response_time_ms": float(stats.avg_response_time or 0)
    }

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler with logging."""
    logger.warning(f"HTTP {exc.status_code}: {exc.detail} - {request.url}")
    return {"error": exc.detail, "status_code": exc.status_code}

if __name__ == "__main__":
    logger.info(f"Starting Gemini API Routing Service on {config.HOST}:{config.PORT}")
    logger.info(f"Database: {config.DB_SERVER}/{config.DB_NAME}")
    logger.info(f"JWT expiration: {config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES} minutes")
    
    uvicorn.run(
        "api_server:app",
        host=config.HOST,
        port=config.PORT,
        reload=False,  # Set to True for development
        log_level="info"
    ) 
"""
Response validation and parsing utilities for LLM outputs.

This module handles validation and parsing of LLM responses to ensure:
- Responses contain valid JSON
- Keywords are from the approved campaign list
- Reasoning quality meets standards (no vague terms) when reasoning is required
- Each patient has the configured number of keywords

The validation is designed to be strict but recoverable, filtering out
invalid keywords while preserving valid ones.
"""

import re
import json
import logging
from typing import Any, Dict, List
import config

logger = logging.getLogger("patient_pipeline")

# List of vague terms that are not allowed in reasoning
VAGUE_TERMS = [
    "common symptom",
    "broadly relevant",
    "possible condition",
    "general health",
    "broad range",
]

def strip_code_fences(text: str) -> str:
    """
    Remove markdown code fences from LLM response text.

    LLMs sometimes wrap JSON responses in markdown code blocks with ```json
    or ``` fences. This function removes these fences to extract clean JSON.

    Args:
        text (str): Raw LLM response text that may contain code fences

    Returns:
        str: Cleaned text with code fences removed

    Example:
        >>> strip_code_fences('```json\\n{"key": "value"}\\n```')
        '{"key": "value"}'
        >>> strip_code_fences('```\\n{"key": "value"}\\n```')
        '{"key": "value"}'
    """
    text = re.sub(r"```[a-zA-Z]*\n?", "", text)
    return text.replace("```", "").strip()

def validate_and_parse(raw: str, campaign: List[str]) -> Dict[str, Any]:
    """
    Validate and parse LLM response for keyword generation.

    Performs comprehensive validation of the LLM response:
    1. Extracts JSON from the response text
    2. Validates that keywords are from the approved campaign list
    3. Filters out invalid keywords while preserving valid ones
    4. Checks reasoning quality for vague terms (if reasoning is required)
    5. Ensures each appointment has the configured number of valid keywords

    Args:
        raw (str): Raw LLM response text
        campaign (List[str]): List of approved campaign keywords

    Returns:
        Dict[str, Any]: Validated and cleaned response data mapping appointment keys
        to their keywords and reasoning

    Raises:
        ValueError: If response doesn't contain valid JSON or has too many
        invalid keywords (>10)

    Example:
        >>> response = '{"1": {"keywords": ["diabetes", "invalid"], "reasoning": {...}}}'
        >>> campaign = ["diabetes", "hypertension"]
        >>> result = validate_and_parse(response, campaign)
        >>> result["1"]["keywords"]
        ['diabetes']  # Invalid keyword filtered out
    """
    # Clean the response and extract JSON content
    cleaned = strip_code_fences(raw)
    first, last = cleaned.find("{"), cleaned.rfind("}")
    if first == -1 or last == -1:
        raise ValueError("Response does not contain JSON object")
    content = cleaned[first : last + 1]
    parsed = json.loads(content)

    # Normalize campaign keywords for case-insensitive, whitespace-insensitive comparison
    camp_set = {c.strip().lower() for c in campaign}
    invalid_keywords = []
    invalid_details = []

    # Validate keywords for each appointment
    for appt_key, appt_data in parsed.items():
        kws = appt_data.get("keywords", [])

        # Find invalid keywords for this appointment
        invalid_for_appointment = [k for k in kws if k.strip().lower() not in camp_set]
        if invalid_for_appointment:
            invalid_keywords.extend(invalid_for_appointment)
            invalid_details.append((appt_key, invalid_for_appointment))

        # Remove invalid keywords from the appointment's list
        appt_data["keywords"] = [k for k in kws if k.strip().lower() in camp_set]

        # Handle reasoning based on configuration
        if config.REASONING_REQUIRED:
            # Remove reasoning for invalid keywords when reasoning is required
            if "reasoning" in appt_data:
                appt_data["reasoning"] = {k: v for k, v in appt_data["reasoning"].items() if k.strip().lower() in camp_set}
        else:
            # Ensure no reasoning field when reasoning is not required
            if "reasoning" in appt_data:
                logger.debug(f"Removing unexpected reasoning field for appointment {appt_key} (reasoning not required)")
                del appt_data["reasoning"]
            # Add empty reasoning field for database compatibility
            appt_data["reasoning"] = {}

        # Log keyword count without warning
        if len(appt_data["keywords"]) != config.KEYWORDS_PER_PATIENT:
            logger.info(f"Appointment {appt_key}: has {len(appt_data['keywords'])} valid keywords after filtering (expected {config.KEYWORDS_PER_PATIENT})")

    # Log and check invalid keyword count
    if len(invalid_keywords) > 0:
        logger.warning(f"Found {len(invalid_keywords)} invalid keywords in batch: {invalid_details}")
    if len(invalid_keywords) > 30:
        raise ValueError(f"Too many invalid keywords in batch: {len(invalid_keywords)} > 30")

    # Check reasoning quality for vague terms (only if reasoning is required)
    if config.REASONING_REQUIRED:
        for appt_key, appt_data in parsed.items():
            for k, reason in appt_data.get("reasoning", {}).items():
                r_low = reason.lower()
                if any(v in r_low for v in VAGUE_TERMS):
                    logger.warning("Vague reasoning detected – appointment=%s kw=%s", appt_key, k)

    return parsed
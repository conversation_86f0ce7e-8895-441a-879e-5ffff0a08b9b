"""
Web.config database connection extractor.

This module extracts database connection settings from Web.config XML files.
It's separated from the main config module to avoid circular imports.
"""

import logging
import xml.etree.ElementTree as ET
import re
from typing import Dict, Optional
from urllib.parse import unquote

logger = logging.getLogger("webconfig_extractor")


def extract_eldataconnection_from_webconfig(config_file_path: str = "../Admin/Web.config") -> Optional[Dict[str, str]]:
    """
    Extract ELDataConnection settings from Web.config file.
    
    Args:
        config_file_path: Path to the Web.config file (default: "../Admin/Web.config")
        
    Returns:
        Dictionary with server, database, username, and password or None if not found
    """
    try:
        # Parse the XML file
        tree = ET.parse(config_file_path)
        root = tree.getroot()
        
        # Find connectionStrings section
        conn_strings_section = root.find('.//connectionStrings')
        if conn_strings_section is None:
            logger.warning("No connectionStrings section found in Web.config")
            return None
        
        # Look for ELDataConnection
        for add_element in conn_strings_section.findall('add'):
            name = add_element.get('name', '')
            if name == 'ELDataConnection':
                conn_string = add_element.get('connectionString', '')
                if conn_string:
                    return _parse_sql_connection_string(conn_string)
        
        logger.warning("ELDataConnection not found in Web.config")
        return None
        
    except ET.ParseError as e:
        logger.error(f"Invalid XML format in Web.config: {e}")
        return None
    except FileNotFoundError:
        logger.error(f"Web.config file not found: {config_file_path}")
        return None
    except Exception as e:
        logger.error(f"Error extracting ELDataConnection from Web.config: {e}")
        return None


def _parse_sql_connection_string(conn_string: str) -> Dict[str, str]:
    """
    Parse a SQL Server connection string into server, database, username, and password.
    
    Args:
        conn_string: The connection string to parse
        
    Returns:
        Dictionary with server, database, username, and password
    """
    # Handle HTML entities
    conn_string = unquote(conn_string.replace('&quot;', '"').replace('&amp;', '&'))
    
    # Extract Entity Framework connection string if present
    if 'provider connection string=' in conn_string:
        match = re.search(r'provider connection string="([^"]*)"', conn_string)
        if match:
            conn_string = match.group(1)
    
    # Parse connection string parameters
    params = {}
    
    # Patterns for the four required parameters
    patterns = {
        'server': r'(?:data source|server|address|addr|network address)\s*=\s*([^;]+)',
        'database': r'(?:initial catalog|database)\s*=\s*([^;]+)',
        'username': r'(?:user id|uid|username|user name)\s*=\s*([^;]+)',
        'password': r'(?:password|pwd)\s*=\s*([^;]+)',
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, conn_string, re.IGNORECASE)
        if match:
            params[key] = match.group(1).strip()
    
    return params


def update_config_variables(config_module, config_file_path: str = "../Admin/Web.config") -> bool:
    """
    Update the keyword database connection settings in the config module from Web.config ELDataConnection.
    
    Args:
        config_module: The config module to update
        config_file_path: Path to the Web.config file (default: "../Admin/Web.config")
        
    Returns:
        True if update was successful, False otherwise
    """
    connection_info = extract_eldataconnection_from_webconfig(config_file_path)
    
    if connection_info:
        # Update the module variables
        if 'server' in connection_info:
            config_module.KEYWORD_DB_SERVER = connection_info['server']
            logger.info(f"Updated KEYWORD_DB_SERVER to: {config_module.KEYWORD_DB_SERVER}")
        
        if 'database' in connection_info:
            config_module.KEYWORD_DB_NAME = connection_info['database']
            logger.info(f"Updated KEYWORD_DB_NAME to: {config_module.KEYWORD_DB_NAME}")
        
        if 'username' in connection_info:
            config_module.KEYWORD_DB_USERNAME = connection_info['username']
            logger.info(f"Updated KEYWORD_DB_USERNAME to: {config_module.KEYWORD_DB_USERNAME}")
        
        if 'password' in connection_info:
            config_module.KEYWORD_DB_PASSWORD = connection_info['password']
            logger.info("Updated KEYWORD_DB_PASSWORD (hidden for security)")
        
        logger.info("Successfully updated keyword database connection settings from Web.config")
        return True
    else:
        logger.error("Failed to extract ELDataConnection from Web.config")
        return False 
"""
<PERSON><PERSON><PERSON> to update EducationalContent_AISetup table in CAEnterprise database
with configuration values from config.py.

This script maps configuration values from config.py to the appropriate columns
in the EducationalContent_AISetup table and provides functionality to:
- Insert new configuration records
- Update existing configuration records
- View current configuration
"""

import pyodbc
import logging
import sys
from datetime import datetime
from typing import Dict, Any, Optional

# Import configuration from config.py
import config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection settings for CAEnterprise
CONFIG_DB_DRIVER = "ODBC Driver 18 for SQL Server"
CONFIG_DB_SERVER = "************"
CONFIG_DB_NAME = "CAEnterprise"
CONFIG_DB_USERNAME = "devAdmin"
CONFIG_DB_PASSWORD = "hOstIR&8l8lWrl=7SlDr"
CONFIG_DB_ENCRYPT = "no"
CONFIG_DB_TRUST_CERT = "yes"

def get_connection_string() -> str:
    """Build connection string for CAEnterprise database."""
    return (
        f"DRIVER={{{CONFIG_DB_DRIVER}}};"
        f"SERVER={CONFIG_DB_SERVER};"
        f"DATABASE={CONFIG_DB_NAME};"
        f"UID={CONFIG_DB_USERNAME};"
        f"PWD={CONFIG_DB_PASSWORD};"
        f"Encrypt={CONFIG_DB_ENCRYPT};"
        f"TrustServerCertificate={CONFIG_DB_TRUST_CERT};"
    )

def map_config_to_db_values() -> Dict[str, Any]:
    """
    Map configuration values from config.py to database column values.
    
    Returns:
        Dictionary mapping database column names to configuration values
    """
    # Map configuration values to database columns
    db_values = {
        'AILLM': 'Gemini',  # LLM provider name
        'AIModel': 'Gemini 1.5 Flash',  # Specific model name
        'AIKey': config.GEMINI_API_KEY,
        'NoOfPatientPerBatch': config.BATCH_SIZE,
        'IsAIReasoning': 'Y' if config.REASONING_REQUIRED else 'N',
        'NoofKeywordsToFetch': config.KEYWORDS_PER_PATIENT,
        'MaxCostPerBatch': config.MAX_COST_USD,
        'LLMTemperature': config.LLM_TEMPERATURE,
        'LLMRtetryAttempts': config.LLM_RETRY_ATTEMPTS,  # Note: column has typo "Rtetry"
        'KeywordFilePath': config.CAMPAIGN_KEYWORDS_CSV,
        'IsActive': 'Y',
        'Source': 'KeywordsV1_Pipeline'
    }
    
    return db_values

def view_current_config() -> None:
    """View current configuration in the EducationalContent_AISetup table."""
    try:
        conn_str = get_connection_string()
        with pyodbc.connect(conn_str, timeout=30) as conn:
            with conn.cursor() as cursor:
                logger.info("Fetching current EducationalContent_AISetup configuration...")
                
                cursor.execute("SELECT * FROM dbo.EducationalContent_AISetup ORDER BY EducationalContentAISetupId")
                rows = cursor.fetchall()
                column_names = [desc[0] for desc in cursor.description]
                
                print("\n" + "="*100)
                print("Current EducationalContent_AISetup Configuration")
                print("="*100)
                
                if rows:
                    for i, row in enumerate(rows):
                        print(f"\nRecord {i+1} (ID: {row[0]}):")
                        print("-" * 50)
                        for j, value in enumerate(row):
                            print(f"  {column_names[j]:<25}: {value}")
                else:
                    print("No configuration records found in the table")
                    
    except pyodbc.Error as e:
        logger.error(f"Database error while viewing configuration: {e}")
    except Exception as e:
        logger.error(f"Unexpected error while viewing configuration: {e}")

def insert_new_config(created_by_id: int = 1) -> bool:
    """
    Insert a new configuration record.
    
    Args:
        created_by_id: ID of the user creating the record
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn_str = get_connection_string()
        db_values = map_config_to_db_values()
        
        with pyodbc.connect(conn_str, timeout=30) as conn:
            with conn.cursor() as cursor:
                logger.info("Inserting new EducationalContent_AISetup configuration...")
                
                insert_sql = """
                INSERT INTO dbo.EducationalContent_AISetup (
                    AILLM, AIModel, AIKey, NoOfPatientPerBatch, IsAIReasoning,
                    NoofKeywordsToFetch, MaxCostPerBatch, LLMTemperature, LLMRtetryAttempts,
                    KeywordFilePath, IsActive, CreatedByID, Source
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_sql, (
                    db_values['AILLM'],
                    db_values['AIModel'],
                    db_values['AIKey'],
                    db_values['NoOfPatientPerBatch'],
                    db_values['IsAIReasoning'],
                    db_values['NoofKeywordsToFetch'],
                    db_values['MaxCostPerBatch'],
                    db_values['LLMTemperature'],
                    db_values['LLMRtetryAttempts'],
                    db_values['KeywordFilePath'],
                    db_values['IsActive'],
                    created_by_id,
                    db_values['Source']
                ))
                
                conn.commit()
                logger.info("New configuration record inserted successfully")
                return True
                
    except pyodbc.Error as e:
        logger.error(f"Database error while inserting configuration: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error while inserting configuration: {e}")
        return False

def update_config(record_id: int, modified_by_id: int = 1) -> bool:
    """
    Update an existing configuration record.
    
    Args:
        record_id: ID of the record to update
        modified_by_id: ID of the user modifying the record
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn_str = get_connection_string()
        db_values = map_config_to_db_values()
        
        with pyodbc.connect(conn_str, timeout=30) as conn:
            with conn.cursor() as cursor:
                logger.info(f"Updating EducationalContent_AISetup configuration record ID: {record_id}")
                
                update_sql = """
                UPDATE dbo.EducationalContent_AISetup SET
                    AILLM = ?, AIModel = ?, AIKey = ?, NoOfPatientPerBatch = ?,
                    IsAIReasoning = ?, NoofKeywordsToFetch = ?, MaxCostPerBatch = ?,
                    LLMTemperature = ?, LLMRtetryAttempts = ?, KeywordFilePath = ?,
                    IsActive = ?, ModifiedByID = ?, ModifiedSrvDTTM = GETDATE(), Source = ?
                WHERE EducationalContentAISetupId = ?
                """
                
                cursor.execute(update_sql, (
                    db_values['AILLM'],
                    db_values['AIModel'],
                    db_values['AIKey'],
                    db_values['NoOfPatientPerBatch'],
                    db_values['IsAIReasoning'],
                    db_values['NoofKeywordsToFetch'],
                    db_values['MaxCostPerBatch'],
                    db_values['LLMTemperature'],
                    db_values['LLMRtetryAttempts'],
                    db_values['KeywordFilePath'],
                    db_values['IsActive'],
                    modified_by_id,
                    db_values['Source'],
                    record_id
                ))
                
                rows_affected = cursor.rowcount
                conn.commit()
                
                if rows_affected > 0:
                    logger.info(f"Configuration record ID {record_id} updated successfully")
                    return True
                else:
                    logger.warning(f"No record found with ID {record_id}")
                    return False
                
    except pyodbc.Error as e:
        logger.error(f"Database error while updating configuration: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error while updating configuration: {e}")
        return False

def print_config_mapping() -> None:
    """Print the mapping between config.py values and database columns."""
    db_values = map_config_to_db_values()
    
    print("\n" + "="*80)
    print("Configuration Mapping (config.py -> Database)")
    print("="*80)
    print(f"{'Database Column':<25} {'Config Value':<30} {'Source':<25}")
    print("-" * 80)
    
    mapping_info = [
        ('AILLM', db_values['AILLM'], 'Hardcoded (Gemini)'),
        ('AIModel', db_values['AIModel'], 'Hardcoded (Gemini 1.5 Flash)'),
        ('AIKey', 'config.GEMINI_API_KEY', f'Value: {config.GEMINI_API_KEY[:10]}...'),
        ('NoOfPatientPerBatch', 'config.BATCH_SIZE', f'Value: {config.BATCH_SIZE}'),
        ('IsAIReasoning', 'config.REASONING_REQUIRED', f'Value: {"Y" if config.REASONING_REQUIRED else "N"}'),
        ('NoofKeywordsToFetch', 'config.KEYWORDS_PER_PATIENT', f'Value: {config.KEYWORDS_PER_PATIENT}'),
        ('MaxCostPerBatch', 'config.MAX_COST_USD', f'Value: {config.MAX_COST_USD}'),
        ('LLMTemperature', 'config.LLM_TEMPERATURE', f'Value: {config.LLM_TEMPERATURE}'),
        ('LLMRtetryAttempts', 'config.LLM_RETRY_ATTEMPTS', f'Value: {config.LLM_RETRY_ATTEMPTS}'),
        ('KeywordFilePath', 'config.CAMPAIGN_KEYWORDS_CSV', f'Value: {config.CAMPAIGN_KEYWORDS_CSV}'),
        ('IsActive', 'Hardcoded', 'Value: Y'),
        ('Source', 'Hardcoded', 'Value: KeywordsV1_Pipeline')
    ]
    
    for db_col, config_source, value_info in mapping_info:
        print(f"{db_col:<25} {config_source:<30} {value_info:<25}")

def main():
    """Main function to handle command line operations."""
    if len(sys.argv) < 2:
        print("\nUsage:")
        print("  python update_ai_setup.py view           - View current configuration")
        print("  python update_ai_setup.py mapping        - Show config mapping")
        print("  python update_ai_setup.py insert         - Insert new configuration")
        print("  python update_ai_setup.py update <id>    - Update existing configuration by ID")
        print("\nExamples:")
        print("  python update_ai_setup.py view")
        print("  python update_ai_setup.py insert")
        print("  python update_ai_setup.py update 1")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'view':
        view_current_config()
    elif command == 'mapping':
        print_config_mapping()
    elif command == 'insert':
        if insert_new_config():
            print("\n✅ Configuration inserted successfully!")
            view_current_config()
        else:
            print("\n❌ Failed to insert configuration")
    elif command == 'update':
        if len(sys.argv) < 3:
            print("Error: Please provide record ID for update")
            print("Usage: python update_ai_setup.py update <id>")
            return
        
        try:
            record_id = int(sys.argv[2])
            if update_config(record_id):
                print(f"\n✅ Configuration record {record_id} updated successfully!")
                view_current_config()
            else:
                print(f"\n❌ Failed to update configuration record {record_id}")
        except ValueError:
            print("Error: Record ID must be a number")
    else:
        print(f"Unknown command: {command}")
        print("Use 'view', 'mapping', 'insert', or 'update <id>'")

if __name__ == "__main__":
    main() 
<?xml version="1.0"?>
<configuration>
  <configSections>
    <!--Log4Net-->
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data" requirePermission="true" />
  </configSections>
  <dataConfiguration defaultDatabase="ELDataConnection" />
  <appSettings>
    <add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
    <add key="DatabaseType" value="SqlServer" />
    <add key="OrganizationId" value="1" />
    <add key="EmailVerifyURLTimeout" value="30" />
    <add key="ConnectionString" value="data source=BASRV02\SQLDEVSRV;initial catalog=820_BA_DB;user id=BASRVDB;pwd=**************" />
    <add key="DefaultLinkAdmin" value="Home.aspx" />
    <add key="DefaultLinkDashboard" value="http://hagw05/Dashboardv1/Dashboard.xbap" />
    <add key="DefaultLinkKiosk" value="http://hapi31/WpfWebcam/WpfHost.xbap" />
    <add key="PasswordExpiryDays" value="90" />
    <add key="PasswordExpiryDaysForAdmin" value="60" />
    <add key="SMTPLastName" value="Sangkar" />
    <add key="SMTPFirstName" value="Singh" />
    <add key="SMTPEmail" value="<EMAIL>" />
    <add key="SMTPSubjectWelcome" value="Welcome to Patient Kiosk Dashboard/Admin" />
    <add key="SMTPSubjectActivated" value="Your Patient Kiosk Dashboard/Admin account has been activated" />
    <add key="SMTPSubjectDeactivated" value="Your Patient Kiosk Dashboard/Admin account has been deactivated" />
    <add key="SMTPSubjectReminding" value="Reminding Your Patient Kiosk Dashboard/Admin Login Information" />
    <add key="SMTPSubjectPWDExpired" value="Your Patient Kiosk Dashboard/Admin login password has expired" />
    <add key="SMTPSubjectResetPWD" value="Your Patient Kiosk Dashboard/Admin login password has been reset" />
    <add key="SMTPSubjectAccUdt" value="Your Patient Kiosk Dashboard/Admin login account has been updated" />
    <add key="SMTPSubjectFooter" value="This e-mail is confidential and may also be privileged. If you are not the intended recipient, please ignore us immediately; you should not copy or use it for any purpose, nor disclose its contents to any other person. This is a system generated e-mail, so please do not reply." />
    <add key="ImageSize" value="40960" />
    <add key="ApiMethodExecutionOrder" value="20" />
    <!--Added by Manisha Kashyap - 03/10/2017 - To get the dashboard base URL to append to UpdatePassword link-->
    <add key="DashboardURL" value="https://baintergydemo.hacheckinasyst.com:20018/Dashboard">
    </add>
    <!-- Added by Umesh VL on 13-NOV-2008, Physical path to upload the Screensaver files -->
    <add key="ScreenSaverFilePath" value="D:\Video\" />
    <!--<add key="ScreenSaverFilePath" value="C:\Kiosk\ScreenSaver\"/>-->
    <!-- Video File Extensions -->
    <add key="VideoFileExtenstion" value=".mpg,.wmv,.wma,.mpeg" />
    <!-- Added by Umesh VL on 25-NOV-2008, Physical path to upload the Theme files -->
    <!--<add key="ThemeFilePath" value="C:\Kiosk\Themes\"/>-->
    <add key="ThemeFilePath" value="D:\Themes\" />
    <!-- Theme File Extensions -->
    <add key="ThemeFileExtenstion" value=".xaml" />
    <add key="MaxUploadLimit" value="11264" />
    <!-- SuperAdmin UserID -->
    <add key="SuperAdminUserID" value="296" />
    <!-- Added by Umesh VL on 01-APR-2009 -->
    <!-- Image File Extensions -->
    <add key="ImageFileExtenstion" value=".jpg" />
    <!-- Added by Umesh VL on 15-APR-2009 -->
    <!-- Document File Upload Limit -->
    <add key="DocumentUploadLimit" value="2048" />
    <add key="ImageUploadFileLimitSizeinKb" value="1024" />
    <add key="PdfUploadFileLimitSizeinKb" value="1024" />
    <!-- Added by Umesh VL on 12-MAY-2009 -->
    <!-- Uploaded Image Dimension -->
    <add key="MaxImageWidth" value="595" />
    <add key="CatalogTypes" value="KioskMessages,Dashboardmessages,Checkintypes,Copyright,creditcardreceipt,Dashboardmessages,Dashboardsystemmessages,Documentimageformat,imageformat,Manualloginoption,Notetype,smtpmaildefinition,ssnoption,USScreenSaverTime,consentconfigattributes,Paymentreports,Questionrule,OrganizationSettings,SystemSettings,ColumnAlias,QSummaryConfigAttributes,EligibilityTradingPartner" />
    <!--for user creations and editing-->
    <add key="UserNameExpression" value="^([a-zA-Z0-9@\.]{4,30})$" />
    <add key="PasswordExpression" value="(?!^[0-9]*$)(?!^[a-zA-Z]*$)^((?=.*[A-Z])(?=.*[$@$!%*#?&amp;])[a-zA-Z0-9$@$!%*#?&amp;]{1,11}[0-9]{4})$" />
    <add key="EmailExpression" value="^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$" />
    <add key="PasswordTextBlockLength" value="15" />
    <add key="UserNameTextBlockLength" value="30" />
    <add key="EmailIdTextBlockLength" value="100" />
    <add key="DisplayWebInterfaceMenu" value="yes" />
    <add key="SMTP_SSL" value="false" />
    <add key="AppType" value="radadmin" />
    <!--Node JS Port-->
    <add key="NodeJSPort" value="1200" />
    <!--EMV Lane Configuration-->
    <add key="TriPOSBaseURL" value="https://triposcert.vantiv.com/cloudapi/v1/lanes" />
    <add key="ApplicationId" value="3057" />
    <add key="ApplicationName" value="CheckinAsyst" />
    <add key="ApplicationVersion" value="6.1" />
    <add key="ResponseWaitMin" value="5" />
    <add key="ClientName" value="Admin" />
    <add key="TenantId" value="Common" />
    <add key="OrgLogoUploadPath" value="PreCheckin\OrgLogos\" />
    <add key="CloudTenantApiUrl" value="https://baintergydemo.hacheckinasyst.com:20018/CloudTenantAPI/api/v1/">
    </add>
    <add key="OrgLogoWidth" value="570" />
    <add key="Salt" value="FWP3F91JWkOWz+BB" />
    <!--Admin@2019-->
    <add key="InstanceToken" value="LHDhd8" />
    <add key="Password" value="povT7GKXeF7B043qdo+x8Efq6awZYzRNCT7dDn+Y/iA=" />
    <add key="DashboardPatServiceURL" value="/Dashboard/Service/PatService.svc" />
    <add key="CheckinasystPatServiceURL" value="/Checkinasyst/Service/PatService.svc" />
    <add key="PreCheckinPatServiceURL" value="/PreCheckin/Service/PatService.svc" />
    <add key="PatientCheckinServiceURL" value="https://baintergydemo.hacheckinasyst.com:20018/CommonService/Services/PatientCheckin.svc/web/" />
    <add key="DisableGoogleAuthPreference" value="true" />
    <add key="DisableConcurrentLogin" value="true" />
  </appSettings>
  <connectionStrings>
    <add name="PatCheckinEntities" connectionString="metadata=res://*/PatCheckinModel.csdl|res://*/PatCheckinModel.ssdl|res://*/PatCheckinModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=BASRV02\SQLDEVSRV;initial catalog=820_BA_DB;user id=BASRVDB;pwd=**************;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ELDataConnection" connectionString="data source=************;initial catalog=V_820_Dev;user id=devAdmin;pwd=**********;8l8lWrl=7SlDr" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <log4net debug="true">
    <appender name="ADONetAppender" type="log4net.Appender.ADONetAppender">
      <bufferSize value="1" />
      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      <commandText value="INSERT INTO Log ([Date],[Thread],[Level],[Logger],[Message],[Exception],[IPAddress]) VALUES (@log_date, @thread, @log_level, @logger, @machine + ' - ' + @message, @exception, @IPAddress)" />
      <parameter>
        <parameterName value="@log_date" />
        <dbType value="DateTime" />
        <layout type="log4net.Layout.RawTimeStampLayout" />
      </parameter>
      <parameter>
        <parameterName value="@thread" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%thread" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_level" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="-1" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@exception" />
        <dbType value="String" />
        <size value="-1" />
        <layout type="log4net.Layout.ExceptionLayout" />
      </parameter>
      <parameter>
        <parameterName value="@IPAddress" />
        <dbType value="String" />
        <size value="200" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{IPAddress}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@machine" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{log4net:HostName}" />
        </layout>
      </parameter>
    </appender>
    <root>
      <level value="ERROR" />
      <appender-ref ref="ADONetAppender" />
    </root>
  </log4net>
  <system.web>
    <!-- 
						Set compilation debug="true" to insert debugging 
						symbols into the compiled page. Because this 
						affects performance, set this value to true only 
						during development.
				-->
    <httpCookies requireSSL="true" />
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" validate="false" />
    </httpHandlers>
    <compilation debug="false" targetFramework="4.7.1">
      <assemblies>
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </buildProviders>
    </compilation>
    <!--
						The <authentication> section enables configuration 
						of the security authentication mode used by 
						ASP.NET to identify an incoming user. 
				-->
    <authentication mode="Windows" />
    <!--
						The <customErrors> section enables configuration 
						of what to do if/when an unhandled error occurs 
						during the execution of a request. Specifically, 
						it enables developers to configure html error pages 
						to be displayed in place of a error stack trace.

				<customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
						<error statusCode="403" redirect="NoAccess.htm" />
						<error statusCode="404" redirect="FileNotFound.htm" />
				</customErrors>
     -->
				
    <httpRuntime maxRequestLength="1048576" requestValidationMode="2.0" enableVersionHeader="false" targetFramework="4.7.1" executionTimeout="600" />
    <!--<identity impersonate="true"/>-->
    <!--<customErrors defaultRedirect="Home.aspx" mode="On"></customErrors>11264-->
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" />
  </system.web>
  <!-- 
				The system.webServer section is required for running ASP.NET AJAX under Internet
				Information Services 7.0.  It is not necessary for previous version of IIS.
		-->
  <!--<system.net>
		<mailSettings>
			<smtp from="<EMAIL>">
				<network host="***********" port="25" userName="<EMAIL>" password="Prakash01!" defaultCredentials="true"/>
			</smtp>
		</mailSettings>
	</system.net>-->
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IAthenaOneServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IEligibilityService" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IIntergyServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="2147483647" maxReceivedMessageSize="2147483647" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="2147483647" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IGEToolkitServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IGreenwayServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IUnityServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="2147483647" maxBufferSize="2147483647" maxReceivedMessageSize="2147483647" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="2147483647" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IGEServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="2147483647" maxReceivedMessageSize="2147483647" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="2147483647" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_IAthenaPracticeServiceAgent" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferSize="2147483647" maxBufferPoolSize="2147483647" maxReceivedMessageSize="2147483647" messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true">
          <readerQuotas maxDepth="32" maxStringContentLength="2147483647" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
      </basicHttpBinding>
      <wsHttpBinding>
        <binding name="WSHttpBinding_IDashboardService" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" bypassProxyOnLocal="false" transactionFlow="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="999999999" maxReceivedMessageSize="999999999" messageEncoding="Text" textEncoding="utf-8" useDefaultWebProxy="true" allowCookies="false">
          <readerQuotas maxDepth="32" maxStringContentLength="999999999" maxArrayLength="999999999" maxBytesPerRead="999999999" maxNameTableCharCount="999999999" />
          <reliableSession ordered="true" inactivityTimeout="00:10:00" enabled="true" />
          <security mode="Transport">
            <transport clientCredentialType="Windows" proxyCredentialType="None" realm="" />
            <message clientCredentialType="Windows" negotiateServiceCredential="true" establishSecurityContext="true" />
          </security>
        </binding>
        <binding name="WSHttpBinding_IDashboardService1" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" bypassProxyOnLocal="false" transactionFlow="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" messageEncoding="Text" textEncoding="utf-8" useDefaultWebProxy="true" allowCookies="false">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <reliableSession ordered="true" inactivityTimeout="00:24:00" enabled="true" />
          <security mode="Transport">
            <transport clientCredentialType="Windows" proxyCredentialType="None" realm="" />
            <message clientCredentialType="Windows" negotiateServiceCredential="true" />
          </security>
        </binding>
      </wsHttpBinding>
    </bindings>
    <client>
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/CommonService/Services/DashboardService.svc" binding="wsHttpBinding" bindingConfiguration="WSHttpBinding_IDashboardService" contract="DashboardService.IDashboardService" name="WSHttpBinding_IDashboardService1">
        <identity>
          <dns value="localhost" />
        </identity>
      </endpoint>
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/AthenaOneAPICommunicator/Service/AthenaOneServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IAthenaOneServiceAgent" contract="AthenaServiceAgent.IAthenaOneServiceAgent" name="BasicHttpBinding_IAthenaOneServiceAgent" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/HA.Eligibility.EligibilityServiceGateway/EligibilityService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IEligibilityService" contract="EligibilityService.IEligibilityService" name="BasicHttpBinding_IEligibilityService" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/IntergyAPICommunicator/Service/IntergyServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IIntergyServiceAgent" contract="IntergyServiceAgent.IIntergyServiceAgent" name="BasicHttpBinding_IIntergyServiceAgent" />
      <endpoint address="https://localhost:54622/Service/GEToolkitServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IGEToolkitServiceAgent" contract="GEToolkitServiceAgent.IGEToolkitServiceAgent" name="BasicHttpBinding_IGEToolkitServiceAgent" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/GreenwayAPICommunicator/Service/GreenwayServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IGreenwayServiceAgent" contract="GreenwayService.IGreenwayServiceAgent" name="BasicHttpBinding_IGreenwayServiceAgent" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/UnityServiceGateWay/Service/UnityServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IUnityServiceAgent" contract="UnityServiceAgent.IUnityServiceAgent" name="BasicHttpBinding_IUnityServiceAgent" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/GECPSAPICommunicator/Service/GEServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IGEServiceAgent" contract="GEServiceAgent.IGEServiceAgent" name="BasicHttpBinding_IGEServiceAgent" />
      <endpoint address="https://baintergydemo.hacheckinasyst.com:20018/AthenaPracticeAPICommunicator/Service/AthenaPracticeServiceAgent.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IAthenaPracticeServiceAgent" contract="AthenaPracticeService.IAthenaPracticeServiceAgent" name="BasicHttpBinding_IAthenaPracticeServiceAgent" />
    </client>
  </system.serviceModel>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="50000000" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <!--<system.diagnostics>
		<trace autoflush="true">
			<listeners>
				<add
						name="textWriterTraceListener"
						type="System.Diagnostics.TextWriterTraceListener"
						initializeData="C:\log\log4net.txt" />
			</listeners>
		</trace>
	</system.diagnostics>-->
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
    </handlers>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
# Windows Task Scheduler Setup Guide

This guide explains how to configure Windows Task Scheduler to run the Campaign Keywords Pipeline daily at a specific time.

## Prerequisites

1. **Python 3.11** installed on the Windows Server
2. **Administrative privileges** to create scheduled tasks
3. **Network connectivity** for database access and LLM API calls
4. **Proper credentials** configured in `config.py`

## Scripts Provided

We've created two scripts for running the pipeline:

1. **`run_pipeline.bat`** - Batch script (traditional Windows)
2. **`run_pipeline.ps1`** - PowerShell script (recommended for Windows Server)

Both scripts provide:
- Automatic Python detection
- Dependency checking
- Comprehensive logging
- Error handling
- Old log cleanup (keeps 30 days)

## Step-by-Step Setup Instructions

### 1. Prepare the Scripts

1. **Update Python Path** (if needed):
   - Edit `run_pipeline.bat` or `run_pipeline.ps1`
   - Update the `PYTHON_PATH` variable if Python is not in your system PATH
   - Common Python 3.11 locations:
     - `C:\Python311\python.exe`
     - `C:\Program Files\Python311\python.exe`
     - `C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe`

2. **Test the Script Manually**:
   ```cmd
   # For batch script:
   C:\path\to\your\project\run_pipeline.bat
   
   # For PowerShell script:
   powershell -ExecutionPolicy Bypass -File "C:\path\to\your\project\run_pipeline.ps1"
   ```

### 2. Create Scheduled Task via GUI

1. **Open Task Scheduler**:
   - Press `Win + R`, type `taskschd.msc`, press Enter
   - Or search for "Task Scheduler" in Start Menu

2. **Create New Task**:
   - Click "Create Task..." (not "Create Basic Task...")
   - This gives you more control over the settings

3. **General Tab**:
   - **Name**: `Campaign Keywords Pipeline`
   - **Description**: `Daily processing of patient data to generate campaign keywords`
   - **Security Options**:
     - Select "Run whether user is logged on or not"
     - Check "Run with highest privileges"
     - Configure for: Windows Server 2016 or your server version

4. **Triggers Tab**:
   - Click "New..."
   - **Begin the task**: On a schedule
   - **Settings**: Daily
   - **Start**: Choose your desired date and time (e.g., 2:00 AM)
   - **Recur every**: 1 days
   - Check "Enabled"
   - Click OK

5. **Actions Tab**:
   - Click "New..."
   
   **For Batch Script**:
   - **Action**: Start a program
   - **Program/script**: `C:\path\to\your\project\run_pipeline.bat`
   - **Start in**: `C:\path\to\your\project`
   
   **For PowerShell Script** (Recommended):
   - **Action**: Start a program
   - **Program/script**: `powershell.exe`
   - **Arguments**: `-ExecutionPolicy Bypass -File "C:\path\to\your\project\run_pipeline.ps1"`
   - **Start in**: `C:\path\to\your\project`

6. **Conditions Tab**:
   - Uncheck "Start the task only if the computer is on AC power"
   - Check "Wake the computer to run this task" (if needed)

7. **Settings Tab**:
   - Check "Allow task to be run on demand"
   - Check "Run task as soon as possible after a scheduled start is missed"
   - Check "If the task fails, restart every": 10 minutes, up to 3 times
   - Check "Stop the task if it runs longer than": 4 hours (adjust based on your needs)
   - Select "Do not start a new instance" for "If the task is already running"

8. **Save the Task**:
   - Click OK
   - Enter the password for the user account when prompted

### 3. Create Scheduled Task via Command Line (Alternative)

For batch script:
```cmd
schtasks /create /tn "Campaign Keywords Pipeline" /tr "C:\path\to\your\project\run_pipeline.bat" /sc daily /st 02:00 /ru SYSTEM /rl HIGHEST /f
```

For PowerShell script:
```cmd
schtasks /create /tn "Campaign Keywords Pipeline" /tr "powershell.exe -ExecutionPolicy Bypass -File 'C:\path\to\your\project\run_pipeline.ps1'" /sc daily /st 02:00 /ru SYSTEM /rl HIGHEST /f
```

### 4. Test the Scheduled Task

1. In Task Scheduler, find your task
2. Right-click and select "Run"
3. Check the "Last Run Result" column
4. Check the batch logs in `batch_logs` directory

## Monitoring and Troubleshooting

### Log Files

The pipeline creates multiple log files:

1. **Batch Job Logs**: `batch_logs\pipeline_batch_[timestamp].log`
   - Contains script execution details
   - Python version checks
   - Dependency installation status
   - Overall execution status

2. **Monitor Logs**:
   ```powershell
   # View live logs
   Get-Content pipeline.log -Wait -Tail 20
   
   # Check specific component logs using grep/findstr
   Select-String -Path pipeline.log -Pattern "health_check"
   Select-String -Path pipeline.log -Pattern "database"
   Select-String -Path pipeline.log -Pattern "llm"
   
   # Check errors
   Select-String -Path pipeline.log -Pattern "ERROR"
   ```

### Common Issues and Solutions

1. **Python Not Found**:
   - Update the `PYTHON_PATH` in the script
   - Ensure Python 3.11 is installed
   - Add Python to system PATH

2. **Permission Denied**:
   - Run Task Scheduler as Administrator
   - Ensure the task runs with appropriate privileges
   - Check file/folder permissions

3. **Database Connection Failed**:
   - Verify network connectivity
   - Check firewall rules
   - Confirm database credentials in `config.py`

4. **Task Doesn't Run**:
   - Check Task Scheduler history
   - Verify trigger settings
   - Ensure "Task Scheduler" service is running

5. **PowerShell Script Blocked**:
   - Set execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine`
   - Or use `-ExecutionPolicy Bypass` in task arguments

### Health Monitoring

The pipeline includes a health check API on port 8080. You can monitor:
- `http://localhost:8080/health` - Overall health status
- `http://localhost:8080/metrics` - Performance metrics

Consider setting up additional monitoring for:
- Task completion status
- Log file errors
- Database connectivity
- API availability

## Best Practices

1. **Schedule During Off-Hours**: Run the pipeline when server load is low
2. **Set Appropriate Timeouts**: Adjust the 4-hour timeout based on your data volume
3. **Monitor Disk Space**: Ensure sufficient space for logs
4. **Regular Backups**: Backup your database before running the pipeline
5. **Test Changes**: Always test script changes manually before updating the scheduled task
6. **Security**: Store sensitive credentials securely, consider using Windows Credential Manager

## Maintenance

1. **Log Rotation**: Scripts automatically clean logs older than 30 days
2. **Dependency Updates**: Periodically update Python packages
3. **Performance Monitoring**: Review execution times and adjust batch sizes if needed
4. **Error Alerts**: Consider setting up email alerts for task failures

## Additional Resources

- [Windows Task Scheduler Documentation](https://docs.microsoft.com/en-us/windows/desktop/taskschd/task-scheduler-start-page)
- [PowerShell Scheduled Tasks](https://docs.microsoft.com/en-us/powershell/module/scheduledtasks/)
- [Python on Windows](https://docs.python.org/3/using/windows.html) 
# Dual Database Architecture

## Overview

The Campaign Keywords Pipeline uses a **dual database architecture** to separate configuration management from operational data:

1. **Configuration Database** - Stores system settings and configuration parameters
2. **Keyword Database** - Stores patient data, appointments, and generated keywords

## Why Two Databases?

### Benefits of Separation

1. **Centralized Configuration Management**
   - Single source of truth for configuration across environments
   - No need to update code when changing database connections
   - Easy migration between development, staging, and production

2. **Security and Access Control**
   - Configuration database can have restricted access
   - Different credentials for configuration vs. operational data
   - Sensitive settings isolated from patient data

3. **Scalability and Performance**
   - Configuration database can be lightweight
   - Keyword database optimized for high-volume operations
   - Independent scaling strategies

4. **Deployment Flexibility**
   - Configuration database can be shared across multiple applications
   - Keyword databases can be environment-specific
   - Easy to point to different keyword databases without code changes

## Database Structure

### Configuration Database

**Table: `dbo.EducationalContent_KeywordConfig`**

| Column | Type | Description |
|--------|------|-------------|
| ConfigKey | NVARCHAR(100) | Unique configuration parameter name |
| ConfigValue | NVARCHAR(500) | Parameter value (as string) |
| ValueType | NVARCHAR(10) | Data type: 'string', 'int', 'float', 'bool' |
| Description | NVARCHAR(500) | Human-readable description |
| CreatedDate | DATETIME | When the config was created |
| ModifiedDate | DATETIME | Last modification timestamp |

**Key Configuration Parameters:**
- `KEYWORD_DB_SERVER` - Target server for keyword database
- `KEYWORD_DB_NAME` - Target database name
- `KEYWORD_DB_USERNAME` - Username for keyword database
- `KEYWORD_DB_PASSWORD` - Password for keyword database
- API keys, processing limits, batch sizes, etc.

### Keyword Database

**Main Tables:**
- `dbo.Appointment_EducationalContent_Keyword` - Appointment keywords
- `dbo.Appointments` - Appointment details
- `dbo.Patient_Result` - Lab results
- `dbo.Patient_Problem` - Medical problems
- `dbo.Patient_Medication` - Current medications
- `dbo.Patient_Allergy` - Known allergies

## Connection Flow

```
1. Application starts
   ↓
2. Connect to Configuration Database (using CONFIG_DB_* settings in config.py)
   ↓
3. Load all configuration from EducationalContent_KeywordConfig table
   ↓
4. Override keyword database connection if specified in config
   ↓
5. Connect to Keyword Database (using loaded or default settings)
   ↓
6. Process patient data and generate keywords
```

## Configuration Examples

### Setting Up Configuration Database

```sql
-- Run sql/config_database_setup.sql to create table and initial values
-- Then customize as needed:

-- Point to production keyword database
UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = 'prod-server.company.com'
WHERE ConfigKey = 'KEYWORD_DB_SERVER';

UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = 'ProductionPatientDB'
WHERE ConfigKey = 'KEYWORD_DB_NAME';
```

### config.py Setup

```python
# Configuration Database (hardcoded - minimal settings needed to bootstrap)
CONFIG_DB_SERVER = "config-server.company.com"
CONFIG_DB_NAME = "ConfigurationDB"
CONFIG_DB_USERNAME = "config_reader"
CONFIG_DB_PASSWORD = "secure_password"

# Keyword Database (defaults - can be overridden by config DB)
DB_SERVER = "dev-server.company.com"
DB_NAME = "DevPatientDB"
DB_USERNAME = "dev_user"
DB_PASSWORD = "dev_password"
```

## Migration Scenarios

### Development to Production

1. Update configuration database:
```sql
UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = CASE ConfigKey
    WHEN 'KEYWORD_DB_SERVER' THEN 'prod-db-server'
    WHEN 'KEYWORD_DB_NAME' THEN 'ProdPatientDB'
    WHEN 'KEYWORD_DB_USERNAME' THEN 'prod_user'
    WHEN 'KEYWORD_DB_PASSWORD' THEN 'prod_secure_pwd'
    ELSE ConfigValue
END
WHERE ConfigKey IN ('KEYWORD_DB_SERVER', 'KEYWORD_DB_NAME', 
                   'KEYWORD_DB_USERNAME', 'KEYWORD_DB_PASSWORD');
```

2. No code changes required - application automatically uses new settings

### Multi-Environment Setup

Different environments can share the same configuration database but point to different keyword databases:

```sql
-- Dev environment
INSERT INTO dbo.EducationalContent_KeywordConfig 
VALUES ('KEYWORD_DB_SERVER', 'dev-server', 'string', 'Dev server');

-- Staging environment (different app instance reads different config)
-- Could use environment-specific config keys or separate config databases
```

## Security Considerations

1. **Configuration Database**
   - Should have read-only access for the application
   - Write access restricted to DBAs/administrators
   - Consider encryption for sensitive values

2. **Keyword Database**
   - Application needs read/write access
   - Follow principle of least privilege
   - Use SQL authentication or integrated security as appropriate

3. **Connection Strings**
   - Never commit production passwords to source control
   - Use secure password storage solutions
   - Rotate credentials regularly

## Troubleshooting

### Configuration Not Loading

1. Check configuration database connectivity:
```python
python -c "from src.config_loader import get_initial_connection_string; print(get_initial_connection_string())"
```

2. Verify table exists:
```sql
SELECT * FROM dbo.EducationalContent_KeywordConfig;
```

3. Check logs for connection errors

### Wrong Database Being Used

1. Verify configuration values:
```sql
SELECT ConfigKey, ConfigValue 
FROM dbo.EducationalContent_KeywordConfig
WHERE ConfigKey LIKE 'KEYWORD_DB_%';
```

2. Check config.py for correct CONFIG_DB_* settings

3. Enable debug logging to see which database is being connected to

## Best Practices

1. **Always use configuration database for environment-specific settings**
2. **Document all configuration keys in the Description field**
3. **Use appropriate data types (ValueType) for validation**
4. **Keep sensitive data encrypted or in secure key vaults**
5. **Implement audit logging for configuration changes**
6. **Test configuration changes in non-production first**
7. **Have rollback procedures for configuration updates** 
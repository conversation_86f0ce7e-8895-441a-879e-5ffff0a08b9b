# Invalid Keywords and Vague Reasoning Handling

## Overview

The system implements a multi-layered approach to handle invalid keywords and vague reasoning:
1. **Prevention** through detailed prompt engineering
2. **Detection** during response parsing
3. **Correction** through automated fixes
4. **Validation** to ensure quality standards

## Invalid Keywords Handling

### Prevention Layer
The prompt includes multiple safeguards:
```
████████████████████████████████████████████████████████████████████████████████████
**STOP: ONLY USE THE FOLLOWING CAMPAIGN KEYWORDS. DO NOT USE ANY OTHER TERMS.**
████████████████████████████████████████████████████████████████████████████████████
```

### Detection & Correction

#### Enhanced Processing Path (`llm_improvements.py`)
1. **Invalid Keyword Detection**
   ```python
   # Check each keyword against campaign list
   if kw.strip().lower() not in campaign_set:
       issues["invalid_keywords"].append((patient_key, kw))
   ```

2. **Smart Correction**
   ```python
   # Try to find closest match using SequenceMatcher
   score = SequenceMatcher(None, kw_lower, campaign_kw).ratio()
   if score >= 0.8:  # 80% similarity threshold
       corrected_keywords.append(best_match)
   ```

3. **Examples from Logs**
   - `"muscle pain"` → Removed (no close match in campaign)
   - `"fever"` → Removed (not in campaign list)
   - `"anemia"` → Removed (not in campaign list)
   - `"insomnia"` → Removed (not in campaign list)

#### Standard Processing Path (`validation.py`)
- Simply removes invalid keywords without attempting correction
- Logs warnings for tracking

### Keyword Count Enforcement

When keywords are removed, the system ensures exactly 7 keywords per patient:

```python
# Fallback keyword categories
fallback_keywords = {
    "general": ["anxiety", "headache", "nausea", "abdominal pain", 
                "loss of energy", "mood swings", "constipation"],
    "mental_health": ["anxiety", "sad", "hopeless", "mood swings", 
                      "loss of interest", "worry", "irritability"],
    "physical": ["headache", "nausea", "abdominal pain", "constipation", 
                 "diarrhea", "body jerking", "tremor"],
    "neurological": ["headache", "migraine", "tremor", "body jerking", 
                     "twitch", "rigidity", "dyskinesia"]
}
```

## Vague Reasoning Handling

### Prevention Layer
The prompt explicitly forbids vague reasoning:
```
**FORBIDDEN Reasoning Examples (NEVER use):**
❌ "Common symptom"
❌ "Broadly relevant symptom"
❌ "Possible underlying condition"
❌ "General health concern"
❌ "Selected to cover broad range"

**REQUIRED Reasoning Examples (ALWAYS use):**
✅ "Patient diagnosed with [condition] documented in medical history"
✅ "Patient prescribed [medication_name] for [condition]"
✅ "Patient has documented [symptom/finding]"
```

### Detection

#### Enhanced Processing (`llm_improvements.py`)
Uses regex patterns for sophisticated detection:
```python
vague_patterns = [
    r"\bcommon\s+(?:symptom|condition|issue)\b",
    r"\bbroadly\s+relevant\b",
    r"\bpossible\s+(?:condition|underlying)\b",
    r"\bgeneral\s+(?:health|concern)\b",
    r"\bmay\s+(?:be|indicate|suggest)\b",
    r"\bcould\s+(?:be|indicate|suggest)\b",
    r"\bselected\s+to\s+cover\b"
]
```

#### Standard Processing (`validation.py`)
Simple string matching:
```python
VAGUE_TERMS = [
    "common symptom",
    "broadly relevant",
    "possible condition",
    "general health",
    "broad range"
]
```

### Handling

1. **Detection**: Vague reasoning is flagged and logged
2. **Quality Score**: Each vague reasoning reduces quality score by 2 points
3. **Warning**: Logged as `"Vague reasoning detected – appointment=X kw=Y"`
4. **No Auto-Correction**: Vague reasoning is not automatically fixed (unlike keywords)

## Quality Metrics

The system tracks quality through scoring:
- **Base Score**: 100 points
- **Invalid Keyword**: -5 points each
- **Wrong Keyword Count**: -10 points
- **Vague Reasoning**: -2 points each

## Real Examples from Logs

### Invalid Keywords Detected
```
WARNING: Removed invalid keyword 'muscle pain' for patient 120_181
WARNING: Removed invalid keyword 'fever' for patient 120_181
WARNING: Removed invalid keyword 'fatigue' for patient 120_181
WARNING: Removed invalid keyword 'anemia' for patient 104_145
```

### Vague Reasoning Detected
```
WARNING: Vague reasoning detected – appointment=58_151 kw=nausea
WARNING: Vague reasoning detected – appointment=95_126 kw=headache
WARNING: Vague reasoning detected – appointment=202_491 kw=anxiety
```

### Quality Scores
```
WARNING: Response quality score: 86.0/100 (minor issues)
WARNING: Response quality score: 50.0/100 (significant issues)
WARNING: Response quality score: -50.0/100 (severe issues)
```

## Configuration

Key configuration options in `config.py`:
- `REASONING_REQUIRED`: Whether to require reasoning (True by default)
- `LLM_TEMPERATURE`: Set to 0.2 for consistent outputs
- `LLM_RETRY_ATTEMPTS`: 4 retries for failed calls

## Best Practices

1. **Monitor Quality Scores**: Responses below 80/100 indicate issues
2. **Review Warnings**: Check logs for patterns in invalid keywords
3. **Update Campaign List**: Ensure campaign keywords are comprehensive
4. **Prompt Tuning**: Adjust prompt examples based on common errors
5. **Fallback Keywords**: Customize based on your domain needs 
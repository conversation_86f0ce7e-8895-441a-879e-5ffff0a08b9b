"""
Client Authentication Utility for Gemini API Routing Service

This utility helps clients authenticate with the API routing service and obtain JWT tokens.
It provides functions to:
- Request JWT tokens from the API server
- Validate and refresh tokens
- Update configuration with authentication credentials

Usage:
    python client_auth.py --client-id "my_client" --secret "demo-secret-key" --server "http://localhost:8000"
"""

import argparse
import requests
import json
import sys
import os
from datetime import datetime, timedelta
import jwt

def get_jwt_token(server_url: str, client_id: str, secret_key: str) -> dict:
    """
    Obtain a JWT token from the API server.
    
    Args:
        server_url (str): URL of the API server
        client_id (str): Client identifier
        secret_key (str): Secret key for authentication
        
    Returns:
        dict: Token response containing access_token, token_type, and expires_in
        
    Raises:
        Exception: If authentication fails
    """
    auth_url = f"{server_url}/auth/token"
    
    try:
        response = requests.post(
            auth_url,
            params={
                "client_id": client_id,
                "secret_key": secret_key
            },
            timeout=30
        )
        
        if response.status_code == 401:
            raise Exception("Invalid credentials. Check your client_id and secret_key.")
        elif response.status_code != 200:
            raise Exception(f"Authentication failed: {response.status_code} - {response.text}")
        
        token_data = response.json()
        print(f"✅ Successfully authenticated as client '{client_id}'")
        print(f"🔑 Token expires in {token_data['expires_in']} seconds")
        
        return token_data
        
    except requests.exceptions.ConnectionError:
        raise Exception(f"Could not connect to API server at {server_url}. Is the server running?")
    except requests.exceptions.Timeout:
        raise Exception("Authentication request timed out.")
    except requests.exceptions.RequestException as e:
        raise Exception(f"HTTP request failed: {e}")

def validate_token(token: str, secret_key: str) -> dict:
    """
    Validate and decode a JWT token.
    
    Args:
        token (str): JWT token to validate
        secret_key (str): Secret key used to sign the token
        
    Returns:
        dict: Decoded token payload
        
    Raises:
        Exception: If token is invalid or expired
    """
    try:
        payload = jwt.decode(token, secret_key, algorithms=["HS256"])
        
        # Check expiration
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp)
            now = datetime.now()
            
            if now >= exp_datetime:
                raise Exception("Token has expired")
            
            time_remaining = exp_datetime - now
            print(f"✅ Token is valid for {client_id}")
            print(f"⏰ Token expires in {time_remaining}")
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise Exception("Token has expired")
    except jwt.InvalidTokenError:
        raise Exception("Invalid token")

def update_config_file(config_file: str, server_url: str, client_id: str, jwt_token: str):
    """
    Update the configuration file with API server settings.
    
    Args:
        config_file (str): Path to config file
        server_url (str): API server URL
        client_id (str): Client ID
        jwt_token (str): JWT token
    """
    try:
        # Read existing config
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                content = f.read()
        else:
            content = ""
        
        # Update or add API server configuration
        lines = content.split('\n')
        updated_lines = []
        api_config_found = False
        
        for line in lines:
            if 'API_SERVER_URL' in line and '=' in line:
                updated_lines.append(f'API_SERVER_URL = "{server_url}"')
                api_config_found = True
            elif 'CLIENT_JWT' in line and '=' in line:
                updated_lines.append(f'CLIENT_JWT = "{jwt_token}"')
            elif 'CLIENT_ID' in line and '=' in line:
                updated_lines.append(f'CLIENT_ID = "{client_id}"')
            else:
                updated_lines.append(line)
        
        # Add configuration if not found
        if not api_config_found:
            updated_lines.extend([
                "",
                "# API Server Configuration (Auto-generated)",
                f'API_SERVER_URL = "{server_url}"',
                f'CLIENT_JWT = "{jwt_token}"',
                f'CLIENT_ID = "{client_id}"'
            ])
        
        # Write updated config
        with open(config_file, 'w') as f:
            f.write('\n'.join(updated_lines))
        
        print(f"✅ Updated configuration file: {config_file}")
        
    except Exception as e:
        print(f"⚠️ Could not update config file: {e}")

def test_api_connection(server_url: str, jwt_token: str, client_id: str):
    """
    Test the API connection with a simple request.
    
    Args:
        server_url (str): API server URL
        jwt_token (str): JWT token
        client_id (str): Client ID
    """
    try:
        # Test health endpoint
        health_response = requests.get(f"{server_url}/health", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ API server is healthy")
            print(f"🔧 Gemini API status: {health_data.get('gemini_api_status', 'unknown')}")
        else:
            print(f"⚠️ Health check failed: {health_response.status_code}")
        
        # Test authenticated endpoint with a simple prompt
        headers = {
            "Authorization": f"Bearer {jwt_token}",
            "Content-Type": "application/json"
        }
        
        test_payload = {
            "prompt": "Hello, this is a test prompt. Please respond with 'API test successful'.",
            "temperature": 0.1,
            "client_id": client_id,
            "max_output_tokens": 50
        }
        
        print("🧪 Testing Gemini API routing...")
        test_response = requests.post(
            f"{server_url}/gemini/query",
            json=test_payload,
            headers=headers,
            timeout=60
        )
        
        if test_response.status_code == 200:
            result = test_response.json()
            print(f"✅ API routing test successful!")
            print(f"📝 Response: {result.get('response', '')[:100]}...")
            print(f"💰 Cost: ${result.get('estimated_cost_usd', 0):.6f}")
            print(f"🔗 Request ID: {result.get('request_id', 'unknown')}")
        else:
            print(f"❌ API routing test failed: {test_response.status_code} - {test_response.text}")
            
    except Exception as e:
        print(f"❌ API connection test failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="Authenticate with Gemini API Routing Service")
    parser.add_argument("--client-id", required=True, help="Client identifier")
    parser.add_argument("--secret", required=True, help="Secret key for authentication")
    parser.add_argument("--server", default="http://localhost:8000", help="API server URL")
    parser.add_argument("--config", default="config.py", help="Configuration file to update")
    parser.add_argument("--test", action="store_true", help="Test the API connection after authentication")
    parser.add_argument("--validate-only", help="Only validate an existing token (provide token string)")
    
    args = parser.parse_args()
    
    try:
        if args.validate_only:
            # Validate existing token
            print(f"🔍 Validating token...")
            payload = validate_token(args.validate_only, "your-super-secret-jwt-key-change-in-production")
            print(f"✅ Token is valid for client: {payload.get('client_id')}")
            return
        
        # Get new token
        print(f"🔐 Authenticating with API server...")
        print(f"📡 Server: {args.server}")
        print(f"👤 Client ID: {args.client_id}")
        
        token_data = get_jwt_token(args.server, args.client_id, args.secret)
        jwt_token = token_data["access_token"]
        
        # Update configuration file
        if args.config:
            update_config_file(args.config, args.server, args.client_id, jwt_token)
        
        # Test connection if requested
        if args.test:
            print("\n" + "="*50)
            print("🧪 TESTING API CONNECTION")
            print("="*50)
            test_api_connection(args.server, jwt_token, args.client_id)
        
        print("\n" + "="*50)
        print("✅ AUTHENTICATION COMPLETE")
        print("="*50)
        print(f"Your JWT token: {jwt_token}")
        print(f"Token expires in: {token_data['expires_in']} seconds")
        print("\nYou can now use the API routing service!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
#!/usr/bin/env pwsh
# PowerShell script to start the Gemini API Routing Service

Write-Host "Starting Gemini API Routing Service..." -ForegroundColor Green
Write-Host ""

# Set environment variables (customize these for your environment)
$env:GEMINI_API_KEY = "AIzaSyCGa46rck1Z1ZLB8gf2bevoAuSACOwogl8"
$env:JWT_SECRET_KEY = "your-super-secret-jwt-key-change-in-production"
$env:API_SERVER_HOST = "0.0.0.0"
$env:API_SERVER_PORT = "8000"

# Database settings (using defaults from config)
$env:DB_DRIVER = "ODBC Driver 18 for SQL Server"
$env:DB_SERVER = "************"
$env:DB_NAME = "CAEnterprise"
$env:DB_USERNAME = "devAdmin"
$env:DB_PASSWORD = "hOstIR&8l8lWrl=7SlDr"
$env:DB_ENCRYPT = "no"
$env:DB_TRUST_CERT = "yes"

Write-Host "Environment configured:" -ForegroundColor Yellow
Write-Host "- Server: $($env:API_SERVER_HOST):$($env:API_SERVER_PORT)"
Write-Host "- Database: $($env:DB_SERVER)/$($env:DB_NAME)"
Write-Host "- Gemini API Key: $($env:GEMINI_API_KEY.Substring(0, 20))..."
Write-Host ""

Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
try {
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install dependencies"
    }
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting API server..." -ForegroundColor Green
Write-Host "Access the API documentation at: http://localhost:$($env:API_SERVER_PORT)/docs" -ForegroundColor Cyan
Write-Host "Health check endpoint: http://localhost:$($env:API_SERVER_PORT)/health" -ForegroundColor Cyan
Write-Host ""

try {
    python api_server.py
} catch {
    Write-Host "ERROR: Failed to start API server" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "API server stopped." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
} 
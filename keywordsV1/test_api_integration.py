"""
End-to-End Test for Gemini API Routing Service

This script tests the complete integration:
1. API server health check
2. JWT authentication
3. Gemini API routing
4. Client code integration

Run this after starting the API server to verify everything works.
"""

import requests
import time
import sys
import json
from datetime import datetime

# Test configuration
API_SERVER_URL = "http://localhost:8000"
CLIENT_ID = "test_client"
SECRET_KEY = "demo-secret-key"

def print_status(message: str, status: str = "INFO"):
    """Print a formatted status message."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    symbols = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARNING": "⚠️"}
    symbol = symbols.get(status, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def test_health_check():
    """Test the health check endpoint."""
    print_status("Testing health check endpoint...")
    
    try:
        response = requests.get(f"{API_SERVER_URL}/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print_status("API server is healthy", "SUCCESS")
            print_status(f"Gemini API status: {health_data.get('gemini_api_status', 'unknown')}")
            return True
        else:
            print_status(f"Health check failed: {response.status_code}", "ERROR")
            return False
            
    except requests.exceptions.ConnectionError:
        print_status("Could not connect to API server. Is it running?", "ERROR")
        return False
    except Exception as e:
        print_status(f"Health check error: {e}", "ERROR")
        return False

def test_authentication():
    """Test JWT authentication."""
    print_status("Testing JWT authentication...")
    
    try:
        response = requests.post(
            f"{API_SERVER_URL}/auth/token",
            params={
                "client_id": CLIENT_ID,
                "secret_key": SECRET_KEY
            },
            timeout=30
        )
        
        if response.status_code == 200:
            token_data = response.json()
            print_status("Authentication successful", "SUCCESS")
            print_status(f"Token expires in: {token_data['expires_in']} seconds")
            return token_data["access_token"]
        else:
            print_status(f"Authentication failed: {response.status_code} - {response.text}", "ERROR")
            return None
            
    except Exception as e:
        print_status(f"Authentication error: {e}", "ERROR")
        return None

def test_gemini_routing(jwt_token: str):
    """Test Gemini API routing."""
    print_status("Testing Gemini API routing...")
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    test_payload = {
        "prompt": "Hello! This is a test of the Gemini API routing service. Please respond with 'API routing test successful' and a brief confirmation that you received this message.",
        "temperature": 0.1,
        "client_id": CLIENT_ID,
        "max_output_tokens": 100
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{API_SERVER_URL}/gemini/query",
            json=test_payload,
            headers=headers,
            timeout=60
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print_status("Gemini API routing successful", "SUCCESS")
            print_status(f"Response time: {response_time:.2f}s")
            print_status(f"Response: {result.get('response', '')[:100]}...")
            print_status(f"Token usage: {result.get('token_usage', {})}")
            print_status(f"Estimated cost: ${result.get('estimated_cost_usd', 0):.6f}")
            print_status(f"Request ID: {result.get('request_id', 'unknown')}")
            return True
        else:
            print_status(f"Gemini routing failed: {response.status_code} - {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"Gemini routing error: {e}", "ERROR")
        return False

def test_client_integration():
    """Test the client integration (using the modified query_gemini function)."""
    print_status("Testing client integration...")
    
    try:
        # Import the modified LLM module
        import config
        from src.llm import query_gemini
        
        # Temporarily set API server configuration
        original_api_url = getattr(config, 'API_SERVER_URL', None)
        original_jwt = getattr(config, 'CLIENT_JWT', None)
        original_client_id = getattr(config, 'CLIENT_ID', None)
        
        # Get a fresh JWT token for testing
        jwt_token = test_authentication()
        if not jwt_token:
            print_status("Cannot test client integration without valid JWT token", "ERROR")
            return False
        
        # Set test configuration
        config.API_SERVER_URL = API_SERVER_URL
        config.CLIENT_JWT = jwt_token
        config.CLIENT_ID = CLIENT_ID
        
        # Test the query_gemini function
        test_prompt = "This is a test of the client integration. Please respond with 'Client integration test successful'."
        
        print_status("Calling query_gemini function...")
        start_time = time.time()
        response = query_gemini(test_prompt)
        response_time = time.time() - start_time
        
        print_status("Client integration successful", "SUCCESS")
        print_status(f"Response time: {response_time:.2f}s")
        print_status(f"Response: {response[:100]}...")
        
        # Restore original configuration
        if original_api_url is not None:
            config.API_SERVER_URL = original_api_url
        if original_jwt is not None:
            config.CLIENT_JWT = original_jwt
        if original_client_id is not None:
            config.CLIENT_ID = original_client_id
        
        return True
        
    except ImportError as e:
        print_status(f"Could not import client modules: {e}", "ERROR")
        return False
    except Exception as e:
        print_status(f"Client integration error: {e}", "ERROR")
        return False

def test_usage_stats(jwt_token: str):
    """Test usage statistics endpoint."""
    print_status("Testing usage statistics...")
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(
            f"{API_SERVER_URL}/stats/{CLIENT_ID}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            stats = response.json()
            print_status("Usage statistics retrieved", "SUCCESS")
            print_status(f"Total requests: {stats.get('total_requests', 0)}")
            print_status(f"Total tokens: {stats.get('total_tokens', 0)}")
            print_status(f"Total cost: ${stats.get('total_cost_usd', 0):.6f}")
            print_status(f"Average response time: {stats.get('avg_response_time_ms', 0):.0f}ms")
            return True
        else:
            print_status(f"Stats retrieval failed: {response.status_code} - {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"Stats error: {e}", "ERROR")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("🧪 GEMINI API ROUTING SERVICE - END-TO-END TEST")
    print("=" * 60)
    print()
    
    # Track test results
    tests = []
    
    # Test 1: Health Check
    print_status("Starting health check test...")
    health_ok = test_health_check()
    tests.append(("Health Check", health_ok))
    print()
    
    if not health_ok:
        print_status("Health check failed. Cannot continue with other tests.", "ERROR")
        sys.exit(1)
    
    # Test 2: Authentication
    print_status("Starting authentication test...")
    jwt_token = test_authentication()
    auth_ok = jwt_token is not None
    tests.append(("Authentication", auth_ok))
    print()
    
    if not auth_ok:
        print_status("Authentication failed. Cannot continue with authenticated tests.", "ERROR")
        sys.exit(1)
    
    # Test 3: Gemini API Routing
    print_status("Starting Gemini API routing test...")
    routing_ok = test_gemini_routing(jwt_token)
    tests.append(("Gemini API Routing", routing_ok))
    print()
    
    # Test 4: Client Integration
    print_status("Starting client integration test...")
    client_ok = test_client_integration()
    tests.append(("Client Integration", client_ok))
    print()
    
    # Test 5: Usage Statistics
    print_status("Starting usage statistics test...")
    stats_ok = test_usage_stats(jwt_token)
    tests.append(("Usage Statistics", stats_ok))
    print()
    
    # Summary
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in tests:
        status = "PASS" if result else "FAIL"
        symbol = "✅" if result else "❌"
        print(f"{symbol} {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print()
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print()
        print_status("🎉 ALL TESTS PASSED! The API routing service is working correctly.", "SUCCESS")
        print_status("You can now use the API server for your Gemini requests.", "SUCCESS")
        sys.exit(0)
    else:
        print()
        print_status(f"❌ {failed} test(s) failed. Please check the errors above.", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main() 
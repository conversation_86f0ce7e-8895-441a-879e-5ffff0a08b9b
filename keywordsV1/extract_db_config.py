#!/usr/bin/env python3
"""
Script to extract database connection settings from Web.config XML file.
Handles both appSettings ConnectionString and connectionStrings section.
"""

import xml.etree.ElementTree as ET
import re
from typing import Dict, List, Optional
from urllib.parse import unquote


class DatabaseConnectionExtractor:
    """Extract database connection settings from Web.config file."""
    
    def __init__(self, config_file_path: str):
        """
        Initialize the extractor with the path to Web.config file.
        
        Args:
            config_file_path: Path to the Web.config file
        """
        self.config_file_path = config_file_path
        self.tree = None
        self.root = None
        self._load_config()
    
    def _load_config(self):
        """Load and parse the XML configuration file."""
        try:
            self.tree = ET.parse(self.config_file_path)
            self.root = self.tree.getroot()
        except ET.ParseError as e:
            raise ValueError(f"Invalid XML format in config file: {e}")
        except FileNotFoundError:
            raise FileNotFoundError(f"Config file not found: {self.config_file_path}")
    
    def _parse_connection_string(self, conn_string: str) -> Dict[str, str]:
        """
        Parse a SQL Server connection string into components.
        
        Args:
            conn_string: The connection string to parse
            
        Returns:
            Dictionary with connection parameters
        """
        # Handle HTML entities
        conn_string = unquote(conn_string.replace('&quot;', '"').replace('&amp;', '&'))
        
        # Extract Entity Framework connection string if present
        if 'provider connection string=' in conn_string:
            # Extract the actual SQL connection string from EF format
            match = re.search(r'provider connection string="([^"]*)"', conn_string)
            if match:
                conn_string = match.group(1)
        
        # Parse connection string parameters
        params = {}
        
        # Common patterns for SQL Server connection strings
        patterns = {
            'server': r'(?:data source|server|address|addr|network address)\s*=\s*([^;]+)',
            'database': r'(?:initial catalog|database)\s*=\s*([^;]+)',
            'username': r'(?:user id|uid|username|user name)\s*=\s*([^;]+)',
            'password': r'(?:password|pwd)\s*=\s*([^;]+)',
            'integrated_security': r'(?:integrated security|trusted_connection)\s*=\s*([^;]+)',
            'connection_timeout': r'(?:connection timeout|timeout)\s*=\s*([^;]+)',
            'command_timeout': r'(?:command timeout)\s*=\s*([^;]+)',
            'encrypt': r'(?:encrypt)\s*=\s*([^;]+)',
            'trust_server_certificate': r'(?:trustservercertificate)\s*=\s*([^;]+)',
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, conn_string, re.IGNORECASE)
            if match:
                params[key] = match.group(1).strip()
        
        return params
    
    def extract_appsettings_connection(self) -> Optional[Dict[str, str]]:
        """
        Extract connection string from appSettings section.
        
        Returns:
            Dictionary with connection parameters or None if not found
        """
        app_settings = self.root.find('.//appSettings')
        if app_settings is None:
            return None
        
        # Look for ConnectionString key
        for add_element in app_settings.findall('add'):
            key = add_element.get('key', '')
            if key == 'ConnectionString':
                conn_string = add_element.get('value', '')
                if conn_string:
                    parsed = self._parse_connection_string(conn_string)
                    parsed['source'] = 'appSettings'
                    parsed['key_name'] = key
                    return parsed
        
        return None
    
    def extract_connectionstrings_section(self) -> List[Dict[str, str]]:
        """
        Extract all connection strings from connectionStrings section.
        
        Returns:
            List of dictionaries with connection parameters
        """
        connections = []
        conn_strings_section = self.root.find('.//connectionStrings')
        
        if conn_strings_section is None:
            return connections
        
        for add_element in conn_strings_section.findall('add'):
            name = add_element.get('name', '')
            conn_string = add_element.get('connectionString', '')
            provider = add_element.get('providerName', '')
            
            if conn_string:
                parsed = self._parse_connection_string(conn_string)
                parsed['source'] = 'connectionStrings'
                parsed['connection_name'] = name
                parsed['provider_name'] = provider
                connections.append(parsed)
        
        return connections
    
    def extract_database_type(self) -> Optional[str]:
        """
        Extract DatabaseType from appSettings.
        
        Returns:
            Database type string or None if not found
        """
        app_settings = self.root.find('.//appSettings')
        if app_settings is None:
            return None
        
        for add_element in app_settings.findall('add'):
            key = add_element.get('key', '')
            if key == 'DatabaseType':
                return add_element.get('value', '')
        
        return None
    
    def extract_all_connections(self) -> Dict[str, any]:
        """
        Extract all database connection information from the config file.
        
        Returns:
            Dictionary containing all connection information
        """
        result = {
            'database_type': self.extract_database_type(),
            'appsettings_connection': self.extract_appsettings_connection(),
            'connectionstrings': self.extract_connectionstrings_section()
        }
        
        return result
    
    def print_connections(self):
        """Print all extracted connection information in a readable format."""
        all_connections = self.extract_all_connections()
        
        print("=" * 60)
        print("DATABASE CONNECTION SETTINGS EXTRACTED FROM WEB.CONFIG")
        print("=" * 60)
        
        # Database Type
        if all_connections['database_type']:
            print(f"\nDatabase Type: {all_connections['database_type']}")
        
        # AppSettings Connection
        if all_connections['appsettings_connection']:
            print(f"\n--- AppSettings Connection ---")
            conn = all_connections['appsettings_connection']
            for key, value in conn.items():
                if key not in ['source', 'key_name']:
                    print(f"{key.replace('_', ' ').title()}: {value}")
        
        # ConnectionStrings Section
        if all_connections['connectionstrings']:
            print(f"\n--- ConnectionStrings Section ---")
            for i, conn in enumerate(all_connections['connectionstrings'], 1):
                print(f"\nConnection {i}: {conn.get('connection_name', 'Unnamed')}")
                print(f"Provider: {conn.get('provider_name', 'Not specified')}")
                for key, value in conn.items():
                    if key not in ['source', 'connection_name', 'provider_name']:
                        print(f"  {key.replace('_', ' ').title()}: {value}")
        
        print("\n" + "=" * 60)
    
    def get_primary_connection(self) -> Optional[Dict[str, str]]:
        """
        Get the primary database connection (usually the first SQL Server connection).
        
        Returns:
            Dictionary with primary connection parameters
        """
        all_connections = self.extract_all_connections()
        
        # First check connectionStrings for SQL Server connections
        for conn in all_connections['connectionstrings']:
            if conn.get('provider_name') == 'System.Data.SqlClient':
                return conn
        
        # Fall back to appSettings connection
        if all_connections['appsettings_connection']:
            return all_connections['appsettings_connection']
        
        # Return first connection if available
        if all_connections['connectionstrings']:
            return all_connections['connectionstrings'][0]
        
        return None


def main():
    """Main function to demonstrate the extractor."""
    config_file = "../Admin/Web.config"
    
    try:
        extractor = DatabaseConnectionExtractor(config_file)
        
        # Print all connections
        extractor.print_connections()
        
        # Get primary connection for use in application
        primary_conn = extractor.get_primary_connection()
        if primary_conn:
            print("\n--- PRIMARY CONNECTION FOR APPLICATION USE ---")
            print(f"Server: {primary_conn.get('server', 'Not specified')}")
            print(f"Database: {primary_conn.get('database', 'Not specified')}")
            print(f"Username: {primary_conn.get('username', 'Not specified')}")
            print(f"Password: {'*' * len(primary_conn.get('password', ''))}")
            
            # Example of how to use in your application
            print("\n--- EXAMPLE USAGE IN YOUR APPLICATION ---")
            print("# You can use these values in your config.py or database connection:")
            print(f"DB_SERVER = \"{primary_conn.get('server', '')}\"")
            print(f"DB_NAME = \"{primary_conn.get('database', '')}\"")
            print(f"DB_USERNAME = \"{primary_conn.get('username', '')}\"")
            print(f"DB_PASSWORD = \"{primary_conn.get('password', '')}\"")
        
    except Exception as e:
        print(f"Error extracting database connections: {e}")


if __name__ == "__main__":
    main()  
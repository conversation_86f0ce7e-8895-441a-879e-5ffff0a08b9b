"""
Centralized configuration for keywordsV1 project.
All configurable values are loaded from database with fallback to defaults.
"""

import logging
from datetime import datetime, timedelta

# Setup basic logging for config loading
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("config")

# Import Web.config extraction functions from utils to avoid circular imports
from utils.webconfig_extractor import extract_eldataconnection_from_webconfig, update_config_variables


def update_keyword_db_config_from_webconfig(config_file_path: str = "../Admin/Web.config") -> bool:
    """
    Update the keyword database connection settings from Web.config ELDataConnection.
    
    Args:
        config_file_path: Path to the Web.config file
        
    Returns:
        True if update was successful, False otherwise
    """
    import sys
    current_module = sys.modules[__name__]
    return update_config_variables(current_module, config_file_path)


# AI Setup Database connection settings - for reading from EducationalContent_AISetup table
CONFIG_DB_DRIVER = "ODBC Driver 18 for SQL Server"
CONFIG_DB_SERVER = "************"
CONFIG_DB_NAME = "CAEnterprise"
CONFIG_DB_USERNAME = "devAdmin"
CONFIG_DB_PASSWORD = "hOstIR&8l8lWrl=7SlDr"
CONFIG_DB_ENCRYPT = "no"
CONFIG_DB_TRUST_CERT = "yes"

# Default configuration values
DEFAULT_CONFIG = {
    # API Keys
    "GEMINI_API_KEY": "AIzaSyCGa46rck1Z1ZLB8gf2bevoAuSACOwogl8",
    
    # Runtime limits and processing parameters
    "MAX_TOKENS_PER_RUN": 100000,
    "MAX_INPUT_TOKENS": 80000,
    "MAX_OUTPUT_TOKENS": 20000,
    "TOKEN_WARNING_THRESHOLD": 0.8,
    "MAX_COST_USD": 5,
    "COST_WARNING_THRESHOLD": 0.7,
    "BATCH_SIZE": 10,
    "LLM_TEMPERATURE": 0.2,
    "LLM_RETRY_ATTEMPTS": 4,
    
    # LLM output configuration
    "REASONING_REQUIRED": True,
    "KEYWORDS_PER_PATIENT": 7,
    
    # Data filtering options
    "PROCESS_DATE": "2025-07-30",  #"2025-07-16"# Will be set to yesterday if None
    
    # Input data configuration
    "CAMPAIGN_KEYWORDS_CSV": "data/CampaignKeywords.csv",
    
    # API Server Configuration (NEW)
    "API_SERVER_URL": "http://localhost:8000",
    "CLIENT_JWT": None,  # Will be set by authentication
    "CLIENT_ID": "keywords_pipeline",
    "JWT_SECRET_KEY": "your-super-secret-jwt-key-change-in-production",
    
    # Logging configuration
    "LOG_LEVEL": "INFO",
    "LOG_FILE": "pipeline.log",
}

# Pricing configuration (USD per 1K tokens)
GEMINI_PRICING = {"input": 0.00025, "output": 0.0005}

# Module-level configuration variables (will be loaded from database and Web.config)
GEMINI_API_KEY = None
MAX_TOKENS_PER_RUN = None
MAX_INPUT_TOKENS = None
MAX_OUTPUT_TOKENS = None
TOKEN_WARNING_THRESHOLD = None
MAX_COST_USD = None
COST_WARNING_THRESHOLD = None
BATCH_SIZE = None
LLM_TEMPERATURE = None
LLM_RETRY_ATTEMPTS = None
REASONING_REQUIRED = None
KEYWORDS_PER_PATIENT = None
PROCESS_DATE = None
CAMPAIGN_KEYWORDS_CSV = None
API_SERVER_URL = "http://localhost:8000"
CLIENT_JWT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.2h9YmbnUUY6yVoA7arlRGJ6ppBNwJteh8RYq41GJvPI"
CLIENT_ID = "keywords_pipeline"
JWT_SECRET_KEY = None
LOG_LEVEL = None
LOG_FILE = None
KEYWORD_DB_SERVER = None
KEYWORD_DB_NAME = None
KEYWORD_DB_USERNAME = None
KEYWORD_DB_PASSWORD = None


def load_configuration():
    """Load configuration from database and apply to module-level variables."""
    logger.info("Loading configuration from EducationalContent_AISetup table...")
    
    # Start with defaults
    config = DEFAULT_CONFIG.copy()
    
    try:
        # Load from AI setup database
        from src.ai_setup_loader import load_ai_setup_from_db, get_ai_setup_connection_string
        
        ai_setup_conn_str = get_ai_setup_connection_string(
            driver=CONFIG_DB_DRIVER,
            server=CONFIG_DB_SERVER,
            database=CONFIG_DB_NAME,
            username=CONFIG_DB_USERNAME,
            password=CONFIG_DB_PASSWORD,
            encrypt=CONFIG_DB_ENCRYPT,
            trust_cert=CONFIG_DB_TRUST_CERT
        )
        
        config = load_ai_setup_from_db(ai_setup_conn_str, fallback_config=DEFAULT_CONFIG)
        logger.info(f"Configuration loaded from database: {CONFIG_DB_SERVER}/{CONFIG_DB_NAME}")
        
    except Exception as e:
        logger.error(f"Failed to load configuration from database: {e}")
        logger.warning("Using default configuration values")
    
    # Preserve existing JWT token if it was already set (e.g., by client_auth.py)
    current_jwt = globals().get('CLIENT_JWT')
    preserve_jwt = current_jwt is not None
    
    # Apply configuration to module-level variables
    globals().update({
        'GEMINI_API_KEY': config.get("GEMINI_API_KEY"),
        'MAX_TOKENS_PER_RUN': config.get("MAX_TOKENS_PER_RUN"),
        'MAX_INPUT_TOKENS': config.get("MAX_INPUT_TOKENS"),
        'MAX_OUTPUT_TOKENS': config.get("MAX_OUTPUT_TOKENS"),
        'TOKEN_WARNING_THRESHOLD': config.get("TOKEN_WARNING_THRESHOLD"),
        'MAX_COST_USD': config.get("MAX_COST_USD"),
        'COST_WARNING_THRESHOLD': config.get("COST_WARNING_THRESHOLD"),
        'BATCH_SIZE': config.get("BATCH_SIZE"),
        'LLM_TEMPERATURE': config.get("LLM_TEMPERATURE"),
        'LLM_RETRY_ATTEMPTS': config.get("LLM_RETRY_ATTEMPTS"),
        'REASONING_REQUIRED': config.get("REASONING_REQUIRED"),
        'KEYWORDS_PER_PATIENT': config.get("KEYWORDS_PER_PATIENT"),
        'PROCESS_DATE': config.get("PROCESS_DATE"),
        'CAMPAIGN_KEYWORDS_CSV': config.get("CAMPAIGN_KEYWORDS_CSV"),
        'API_SERVER_URL': config.get("API_SERVER_URL"),
        'CLIENT_JWT': current_jwt if preserve_jwt else config.get("CLIENT_JWT"),
        'CLIENT_ID': config.get("CLIENT_ID"),
        'JWT_SECRET_KEY': config.get("JWT_SECRET_KEY"),
        'LOG_LEVEL': config.get("LOG_LEVEL"),
        'LOG_FILE': config.get("LOG_FILE"),
        'KEYWORD_DB_SERVER': None,  # Will be loaded from Web.config
        'KEYWORD_DB_NAME': None,    # Will be loaded from Web.config
        'KEYWORD_DB_USERNAME': None, # Will be loaded from Web.config
        'KEYWORD_DB_PASSWORD': None, # Will be loaded from Web.config
    })
    
    if preserve_jwt:
        logger.info(f"Preserved existing JWT token (length: {len(current_jwt)} chars)")
    
    # Set PROCESS_DATE to yesterday if not specified
    if not PROCESS_DATE:
        yesterday = datetime.now() - timedelta(days=1)
        globals()['PROCESS_DATE'] = yesterday.strftime('%Y-%m-%d')
        logger.info(f"PROCESS_DATE set to yesterday: {PROCESS_DATE}")


def validate_config():
    """
    Validate that all required configuration values are present.
    
    Raises:
        EnvironmentError: If any required configuration values are missing.
    """
    missing = []
    if not GEMINI_API_KEY:
        missing.append("GEMINI_API_KEY")
    if not KEYWORD_DB_SERVER:
        missing.append("KEYWORD_DB_SERVER (from Web.config)")
    if not KEYWORD_DB_NAME:
        missing.append("KEYWORD_DB_NAME (from Web.config)")
    if not KEYWORD_DB_USERNAME:
        missing.append("KEYWORD_DB_USERNAME (from Web.config)")
    if not KEYWORD_DB_PASSWORD:
        missing.append("KEYWORD_DB_PASSWORD (from Web.config)")
    
    if missing:
        raise EnvironmentError(f"Missing required config: {', '.join(missing)}")


# Load configuration from database
load_configuration()

# Load keyword database connection settings from Web.config
try:
    logger.info("Loading keyword database connection settings from Web.config...")
    success = update_keyword_db_config_from_webconfig()
    if not success:
        raise EnvironmentError("Failed to load database connection settings from Web.config")
    
    # Validate that all required settings were loaded
    if not all([KEYWORD_DB_SERVER, KEYWORD_DB_NAME, KEYWORD_DB_USERNAME, KEYWORD_DB_PASSWORD]):
        missing = [name for name, value in [
            ("KEYWORD_DB_SERVER", KEYWORD_DB_SERVER),
            ("KEYWORD_DB_NAME", KEYWORD_DB_NAME),
            ("KEYWORD_DB_USERNAME", KEYWORD_DB_USERNAME),
            ("KEYWORD_DB_PASSWORD", KEYWORD_DB_PASSWORD)
        ] if not value]
        raise EnvironmentError(f"Missing required database connection settings from Web.config: {', '.join(missing)}")
    
    logger.info(f"Successfully loaded keyword database settings: {KEYWORD_DB_SERVER}/{KEYWORD_DB_NAME}")
    
except Exception as e:
    logger.error(f"CRITICAL ERROR: Could not load keyword database settings from Web.config: {e}")
    logger.error("Please ensure Web.config file exists and contains valid ELDataConnection settings.")
    raise EnvironmentError(f"Failed to load required database connection settings: {e}")

# Database connection settings for backward compatibility
DB_DRIVER = "ODBC Driver 18 for SQL Server"
DB_SERVER = KEYWORD_DB_SERVER
DB_NAME = KEYWORD_DB_NAME
DB_USERNAME = KEYWORD_DB_USERNAME
DB_PASSWORD = KEYWORD_DB_PASSWORD
DB_ENCRYPT = "no"
DB_TRUST_CERT = "yes"
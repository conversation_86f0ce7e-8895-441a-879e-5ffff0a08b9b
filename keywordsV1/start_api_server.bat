@echo off
echo Starting Gemini API Routing Service...
echo.

REM Set environment variables (customize these for your environment)
set GEMINI_API_KEY=AIzaSyCGa46rck1Z1ZLB8gf2bevoAuSACOwogl8
set JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
set API_SERVER_HOST=0.0.0.0
set API_SERVER_PORT=8000

REM Database settings (using defaults from config)
set DB_DRIVER=ODBC Driver 18 for SQL Server
set DB_SERVER=************
set DB_NAME=CAEnterprise
set DB_USERNAME=devAdmin
set DB_PASSWORD=hOstIR^&8l8lWrl=7SlDr
set DB_ENCRYPT=no
set DB_TRUST_CERT=yes

echo Environment configured:
echo - Server: %API_SERVER_HOST%:%API_SERVER_PORT%
echo - Database: %DB_SERVER%/%DB_NAME%
echo - Gemini API Key: %GEMINI_API_KEY:~0,20%...
echo.

echo Installing/updating dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Starting API server...
echo Access the API documentation at: http://localhost:%API_SERVER_PORT%/docs
echo Health check endpoint: http://localhost:%API_SERVER_PORT%/health
echo.

python api_server.py

echo.
echo API server stopped.
pause 